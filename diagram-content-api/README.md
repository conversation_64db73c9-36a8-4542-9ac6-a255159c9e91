# Diagram Generation API

A FastAPI-based service that generates diagram definitions from natural language prompts.

## Overview

This API allows users to create various types of diagrams by simply describing what they want in plain English. The service uses AI to interpret the request and generate the appropriate diagram definition in JSON format.

## Features

- **Natural Language Processing**: Describe your diagram needs in plain English
- **Multiple Diagram Types**: Support for Venn diagrams, polygon diagrams, and angle diagrams
- **Customizable Parameters**: Control appearance, labels, and behavior
- **Animation Support**: Add transitions and animations to diagrams

## Getting Started

### Prerequisites

- Python 3.8+
- pip

### Installation

1. Clone the repository:
   ```
   git clone https://github.com/yourusername/diagram-generation-api.git
   cd diagram-generation-api
   ```

2. Install dependencies:
   ```
   pip install -r requirements.txt
   ```

3. Set up environment variables:
   ```
   cp .env.example .env
   ```
   Edit `.env` to add your API keys.

4. Run the server:
   ```
   uvicorn app.main:app --reload
   ```

The API will be available at `http://localhost:8000`.

## API Usage

### Generate a Diagram

```
POST /diagram/generate
```

Request body:
```json
{
  "prompt": "Create a Venn diagram showing the overlap between people who like coffee and people who like tea"
}
```

Response:
```json
{
  "diagram": {
    "diagramType": "venn",
    "data": [
      {
        "sets": ["Coffee"],
        "size": 12,
        "label": "Coffee(12)"
      },
      {
        "sets": ["Tea"],
        "size": 10,
        "label": "Tea(10)"
      },
      {
        "sets": ["Coffee", "Tea"],
        "size": 5,
        "label": "5"
      }
    ]
  }
}
```

## Diagram Types

The API supports the following diagram types:

- **Venn Diagrams**: Represent relationships between sets
- **Polygon Diagrams**: Create geometric shapes with vertices and edges
- **Angle Diagrams**: Visualize angles with customizable properties

For detailed information about diagram parameters and examples, see [Diagram Types Documentation](diagram_types_docs.md).

## Example Prompts

Here are some example prompts you can use:

### Venn Diagram
- "Create a Venn diagram showing the overlap between science fiction readers and fantasy readers"
- "Make a Venn diagram of students who play sports and students who are in the drama club"

### Polygon Diagram
- "Draw a hexagon with labeled vertices A through F"
- "Create a triangle with sides labeled a, b, and c"

### Angle Diagram
- "Show a 45-degree angle that can be adjusted"
- "Create an angle ABC where B is the vertex and the angle is 30 degrees"

## Architecture

The API uses a multi-stage process to generate diagrams:

1. **Intent Recognition**: Determines which type of diagram to create
2. **Parameter Extraction**: Extracts specific parameters from the prompt
3. **Diagram Generation**: Builds the final diagram definition

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

This project is licensed under the MIT License - see the LICENSE file for details.