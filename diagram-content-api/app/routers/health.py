from fastapi import APIRouter
from datetime import datetime, timezone
import psutil
import os

router = APIRouter(prefix="/health", tags=["Health"])


@router.get("/")
async def health_check():
    """
    Simple health check endpoint that returns the status of the application.
    """
    return {
        "status": "healthy",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "service": "diagram-content-api"
    }


@router.get("/detailed")
async def detailed_health_check():
    """
    Detailed health check endpoint that includes system information.
    """
    try:
        # Get system information
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        return {
            "status": "healthy",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "service": "diagram-content-api",
            "system": {
                "cpu_percent": psutil.cpu_percent(interval=1),
                "memory": {
                    "total": memory.total,
                    "available": memory.available,
                    "percent": memory.percent
                },
                "disk": {
                    "total": disk.total,
                    "free": disk.free,
                    "percent": (disk.used / disk.total) * 100
                }
            },
            "process": {
                "pid": os.getpid(),
                "memory_info": dict(psutil.Process().memory_info()._asdict())
            }
        }
    except Exception as e:
        return {
            "status": "degraded",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "service": "diagram-content-api",
            "error": str(e)
        }


@router.get("/ready")
async def readiness_check():
    """
    Readiness check endpoint to verify if the service is ready to handle requests.
    This can be used by container orchestrators like Kubernetes.
    """
    try:
        # You can add specific checks here, such as:
        # - Database connectivity
        # - External service availability
        # - Model loading status
        
        # For now, we'll do a simple check
        return {
            "status": "ready",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "service": "diagram-content-api"
        }
    except Exception as e:
        return {
            "status": "not_ready",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "service": "diagram-content-api",
            "error": str(e)
        }


@router.get("/live")
async def liveness_check():
    """
    Liveness check endpoint to verify if the service is alive.
    This can be used by container orchestrators like Kubernetes.
    """
    return {
        "status": "alive",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "service": "diagram-content-api"
    }
