from fastapi import APIRouter, Form, HTTPException, Query
from fastapi.responses import FileResponse
import torchaudio as ta
import os
import torch
from chatterbox.tts import ChatterboxTTS
from typing import Optional
import pathlib

router = APIRouter(prefix="/tts", tags=["Text-to-Speech"])

# Create audio files directory if it doesn't exist
audio_dir = pathlib.Path("audio_files")
audio_dir.mkdir(exist_ok=True)

# Initialize model variable
model = None
# Save original torch.load
original_load = torch.load

def cpu_load(*args, **kwargs):
    kwargs['map_location'] = torch.device('cpu')
    return original_load(*args, **kwargs)

def load_model():
    global model
    if model is not None:
        return model

    try:
        device = "cuda" if torch.cuda.is_available() else "cpu"
        print(f"Trying to load TTS model with device={device}...")

        if device == "cuda":
            # Let Chatterbox load normally (no monkey-patch needed)
            model = ChatterboxTTS.from_pretrained(device="cuda")
        else:
            # Temporarily patch torch.load to force CPU map
            print("Patching torch.load to force CPU load...")
            torch.load = cpu_load

            model = ChatterboxTTS.from_pretrained(device="cpu")
            print("TTS model loaded successfully on CPU.")
    except Exception as e:
        print(f"Failed to load TTS model: {str(e)}")
        raise RuntimeError("Failed to load TTS model")
    finally:
        # Always restore torch.load
        torch.load = original_load

    return model


@router.post("/generate")
async def generate(text: str = Form(...), filename: Optional[str] = Form(None)):
    """
    Generate speech from text and save it with an optional filename.
    If no filename is provided, a default name will be used.
    """
    # Ensure model is loaded
    if model is None:
        load_model()
    
    # Sanitize filename or use default
    if not filename:
        filename = "output.wav"
    else:
        # Ensure filename has .wav extension and is safe
        filename = "".join(c for c in filename if c.isalnum() or c in "._- ").rstrip()
        if not filename.endswith(".wav"):
            filename += ".wav"
    
    # Generate the speech waveform
    wav = model.generate(text)
    
    # Save the audio to a file in the audio_files directory
    output_path = audio_dir / filename
    ta.save(str(output_path), wav, model.sr)
    
    # Return the file as a response
    return {
        "filename": filename,
        "url": f"/tts/audio/{filename}",
        "message": "Audio generated successfully"
    }

@router.get("/audio/{filename}")
async def get_audio(filename: str):
    """
    Retrieve a previously generated audio file by filename.
    """
    # Sanitize filename
    filename = "".join(c for c in filename if c.isalnum() or c in "._- ").rstrip()
    if not filename.endswith(".wav"):
        filename += ".wav"
    
    file_path = audio_dir / filename
    
    if not file_path.exists():
        raise HTTPException(status_code=404, detail=f"Audio file {filename} not found")
    
    return FileResponse(str(file_path), media_type="audio/wav")

@router.get("/list")
async def list_audio_files():
    """
    List all available audio files.
    """
    files = [f.name for f in audio_dir.glob("*.wav")]
    return {"files": files}

# Try to load the model at module initialization
try:
    load_model()
    print("TTS model loaded successfully")
except Exception as e:
    print(f"Warning: TTS model failed to load at startup: {str(e)}")
    print("Will attempt to load model when first request is received")
