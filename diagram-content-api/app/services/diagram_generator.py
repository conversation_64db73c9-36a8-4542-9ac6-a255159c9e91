from typing import Union, Any
from langchain.prompts import PromptTemplate
from langchain_openai import ChatOpenAI
from langchain_core.runnables import RunnableLamb<PERSON>, RunnableBranch
from app.chains.venn.generate_venn_diagram_data import generate_venn_diagram_data
from app.chains.polygon.generate_polygon_diagram_data import generate_polygon_diagram_data
from app.chains.angle.generate_angle_diagram_data import generate_angle_diagram_data  # Adjust to correct angle diagram chain!
from app.models.diagram_types import VennDiagramParams, PolygonDiagramParams, AngleDiagramParams
from app.core.config import settings


# ────────────────────────────────────────────────
# 🔹 LLM
# ────────────────────────────────────────────────
chat = ChatOpenAI(
    model="gpt-4o-mini",
    api_key=settings.api_key,
    temperature=0,
)


# ────────────────────────────────────────────────
# 🔹 Route Selector
# ────────────────────────────────────────────────
route_prompt = PromptTemplate.from_template("""
Analyze the prompt and determine the best diagram type:
- If it describes set relationships (overlapping, disjoint, intersections), return "venn".
- If it describes geometric shapes (vertices, edges), return "polygon".
- If it describes angles (degrees, radians, turning, arc, ∠ABC), return "angle".

Return only one of: "venn", "polygon", "angle".

Prompt: {input}
""")

route_selector = route_prompt | chat | RunnableLambda(lambda response: response.content.strip().lower())


# ────────────────────────────────────────────────
# 🔹 Multi-route Branch
# ────────────────────────────────────────────────
multi_route_chain = RunnableBranch(
        (RunnableLambda(lambda input: route_selector.invoke(input) == "venn"), generate_venn_diagram_data),
        (RunnableLambda(lambda input: route_selector.invoke(input) == "polygon"), generate_polygon_diagram_data),
        (RunnableLambda(lambda input: route_selector.invoke(input) == "angle"), generate_angle_diagram_data),
        (RunnableLambda(lambda input: "Unknown diagram type")),
)



# ────────────────────────────────────────────────
# 🔹 Final Exported Function
# ────────────────────────────────────────────────
async def get_diagram_definition_from_prompt(prompt: str) -> Union[
    VennDiagramParams, PolygonDiagramParams, AngleDiagramParams, dict
]:
    result = await multi_route_chain.ainvoke({"input": prompt})

    if isinstance(result, dict) and result.get("error"):
        print("❌ Invalid diagram type selected.")
        raise ValueError("Output did not match any valid diagram definition")

    return result
