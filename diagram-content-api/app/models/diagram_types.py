# diagram_types.py

from typing import Optional, List, Literal, Union
from pydantic import BaseModel


class BaseDiagramParams(BaseModel):
    diagramType: str


# Graph-related types
class DataPoint(BaseModel):
    x: float
    y: float
    label: Optional[str] = None
    color: Optional[str] = None
    size: Optional[float] = None


class GraphSeries(BaseModel):
    id: str
    name: str
    data: List[DataPoint]
    type: Literal['line', 'scatter', 'bar', 'area', 'spline', 'step']
    color: Optional[str] = None
    strokeWidth: Optional[float] = None
    fillOpacity: Optional[float] = None
    visible: Optional[bool] = None
    animated: Optional[bool] = None
    animationDuration: Optional[float] = None
    animationDelay: Optional[float] = None


class GraphAxis(BaseModel):
    label: Optional[str] = None
    min: Optional[float] = None
    max: Optional[float] = None
    tickCount: Optional[int] = None
    tickFormat: Optional[str] = None
    gridVisible: Optional[bool] = None
    gridColor: Optional[str] = None
    gridOpacity: Optional[float] = None
    labelColor: Optional[str] = None
    labelFontSize: Optional[float] = None


class GraphLegend(BaseModel):
    visible: Optional[bool] = None
    position: Optional[Literal['top', 'bottom', 'left', 'right', 'top-left', 'top-right', 'bottom-left', 'bottom-right']] = None
    orientation: Optional[Literal['horizontal', 'vertical']] = None
    backgroundColor: Optional[str] = None
    borderColor: Optional[str] = None
    textColor: Optional[str] = None
    fontSize: Optional[float] = None


class GraphTooltip(BaseModel):
    enabled: Optional[bool] = None
    format: Optional[str] = None
    backgroundColor: Optional[str] = None
    textColor: Optional[str] = None
    borderColor: Optional[str] = None
    fontSize: Optional[float] = None


class GraphInteraction(BaseModel):
    zoom: Optional[bool] = None
    pan: Optional[bool] = None
    select: Optional[bool] = None
    brush: Optional[bool] = None
    crosshair: Optional[bool] = None
    hover: Optional[bool] = None


class GraphAnimation(BaseModel):
    enabled: Optional[bool] = None
    duration: Optional[float] = None
    easing: Optional[Literal['linear', 'easeIn', 'easeOut', 'easeInOut', 'circIn', 'circOut', 'circInOut', 'backIn', 'backOut', 'backInOut']] = None
    stagger: Optional[float] = None


class GraphMargin(BaseModel):
    top: Optional[float] = None
    right: Optional[float] = None
    bottom: Optional[float] = None
    left: Optional[float] = None


class GraphCustomTheme(BaseModel):
    backgroundColor: Optional[str] = None
    textColor: Optional[str] = None
    gridColor: Optional[str] = None
    axisColor: Optional[str] = None


class GraphDiagramParams(BaseDiagramParams):
    diagramType: Literal["graph"]
    mode: Literal['plot', 'interactive']
    width: Optional[float] = None
    height: Optional[float] = None
    series: List[GraphSeries]
    xAxis: Optional[GraphAxis] = None
    yAxis: Optional[GraphAxis] = None
    legend: Optional[GraphLegend] = None
    tooltip: Optional[GraphTooltip] = None
    interaction: Optional[GraphInteraction] = None
    animation: Optional[GraphAnimation] = None
    backgroundColor: Optional[str] = None
    margin: Optional[GraphMargin] = None
    theme: Optional[Literal['light', 'dark', 'custom']] = None
    customTheme: Optional[GraphCustomTheme] = None


class VennSetItem(BaseModel):
    sets: List[str]
    size: int
    label: Optional[str] = None


class VennDiagramParams(BaseDiagramParams):
    diagramType: Literal["venn"]
    data: List[VennSetItem]


class Point(BaseModel):
    x: float
    y: float


class PolygonDiagramParams(BaseDiagramParams):
    diagramType: Literal["polygon"]
    width: float
    height: float
    points: List[Point]
    fill: Optional[str] = None
    stroke: Optional[str] = None
    strokeWidth: Optional[float] = None
    pointLabels: Optional[List[str]] = None
    lineLabels: Optional[List[str]] = None


class AngleLabel(BaseModel):
    label: Optional[str] = None
    angleDeg: Optional[float] = None
    angleRad: Optional[float] = None
    showDegreeSymbol: Optional[bool] = None
    color: Optional[str] = None
    fontSize: Optional[float] = None


class AngleDiagramParams(BaseDiagramParams):
    diagramType: Literal["angle"]
    fromAngleDeg: Optional[float] = None
    toAngleDeg: float
    draggable: Optional[Literal["from", "to", "both", "none"]] = None
    snapIncrement: Optional[float] = None
    drawClockwise: Optional[bool] = None
    arcColor: Optional[str] = None
    lineColor: Optional[str] = None
    labelColor: Optional[str] = None
    fontSize: Optional[float] = None
    labels: Optional[dict] = None
    angleLabel: Optional[str] = None
    highlightColor: Optional[str] = None
    showHandles: Optional[bool] = None


TransitionType = Literal[
    "fade",
    "zoom",
    "slide",
    "flip",
    "drop",
    "rotate",
    "warp",
    "diagonal",
    "distortFade",
    "ripple",
    "glitch",
    "explode",
    "orbit",
]


AnimationType = Literal[
    "infiniteRotate",
    "infiniteBounce",
    "infinitePulse",
    "infiniteWiggle",
    "infiniteGlow",
]


DiagramDefinition = Union[VennDiagramParams, PolygonDiagramParams, AngleDiagramParams, GraphDiagramParams]
