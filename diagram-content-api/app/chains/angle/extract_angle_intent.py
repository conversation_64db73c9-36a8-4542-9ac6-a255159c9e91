from typing import Optional, Literal
from pydantic import BaseModel
from langchain.prompts import PromptTemplate
from langchain_openai import ChatOpenAI
from app.core.config import settings


# 1. Schema
class AngleIntent(BaseModel):
    toAngleDeg: float
    fromAngleDeg: Optional[float] = 0
    drawClockwise: Optional[bool] = None
    draggable: Optional[Literal["from", "to", "both", "none"]] = None
    snapIncrement: Optional[float] = None


# 2. Prompt
angle_intent_prompt = PromptTemplate.from_template("""
You are extracting angle diagram intent from a user prompt.

Return only valid JSON with the following structure:
{{
  "toAngleDeg": number,
  "fromAngleDeg": number | null,         // optional, default to 0
  "drawClockwise": boolean | null,
  "draggable": "from" | "to" | "both" | "none" | null,
  "snapIncrement": number | null
}}

Guidelines:
- Default fromAngleDeg to 0 if not specified.
- If user says “clockwise” or “counterclockwise”, reflect that in drawClockwise, if unclear return false by default.
- If they mention dragging handles (e.g., "adjustable", "move ends"), set draggable accordingly.
- snapIncrement could be 15, 30, etc. if user says "snap to 15 degrees", etc.

Prompt: {prompt}
""")


# 3. Chain
llm = ChatOpenAI(
    model="gpt-4o-mini",
    temperature=0,
    api_key=settings.api_key,  # Replace with your actual API key or environment variable
).with_structured_output(AngleIntent)

angle_intent_chain = angle_intent_prompt | llm
