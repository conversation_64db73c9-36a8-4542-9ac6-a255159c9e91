from pydantic import BaseModel
from langchain.prompts import PromptTemplate
from langchain_openai import ChatOpenAI
from langchain_core.runnables import Run<PERSON><PERSON><PERSON>amb<PERSON>
from app.core.config import settings


# 1. Schema
class NumSetsOutput(BaseModel):
    numSets: int


# 2. LLM
chat = ChatOpenAI(
    model="gpt-4o-mini",
    api_key=settings.api_key,
    temperature=0,
)


# 3. Prompt Template
venn_number_extractor_prompt = PromptTemplate.from_template("""
Analyze the following prompt and determine how many distinct sets are mentioned in the context of a Venn diagram.

Return only valid JSON in the format:
{{
  "numSets": number
}}

Rules:
- Count only unique sets or categories mentioned for inclusion in the diagram.
- Treat cases where a group is described that do not fit into any of the clear sets as an additional set
- Do NOT include any additional information or explanation.
- Do NOT guess beyond what is clearly implied in the prompt.
- Return only valid JSON — no extra text.

Prompt: {input}
""")


# 4. Chain
num_sets_chain = (
    venn_number_extractor_prompt
    | chat.with_structured_output(NumSetsOutput)
    | RunnableLambda(lambda output: _log_and_return_num_sets(output))
)


def _log_and_return_num_sets(output: NumSetsOutput) -> NumSetsOutput:
    print("🔢 Extracted numSets:", output.numSets)
    return output
