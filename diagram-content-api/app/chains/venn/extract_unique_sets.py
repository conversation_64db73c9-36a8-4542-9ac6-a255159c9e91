from typing import List
from pydantic import BaseModel
from langchain.prompts import PromptTemplate
from langchain_openai import <PERSON>tOpenAI
from langchain_core.runnables import <PERSON>nableLambda
from app.core.config import settings


# 1. Schema for Venn output (unique individual sets only)
class VennSetItem(BaseModel):
    sets: List[str]
    size: int
    label: str


class VennUniqueSetsOutput(BaseModel):
    diagramType: str
    data: List[VennSetItem]


# 2. Chat model
chat = ChatOpenAI(
    model="gpt-4o-mini",
    api_key=settings.api_key,
    temperature=0,
)


# 3. Prompt template for generating unique (non-overlapping) Venn set regions
venn_unique_sets_prompt = PromptTemplate.from_template("""
You are creating data for a Venn diagram that contains only individual sets — no overlaps or intersections.

The original user prompt was:
{initialPrompt}

There are {numSets} distinct sets.

Output valid JSON in this format:
{{
  "diagramType": "venn",
  "data": [
    {{"sets": [string], "size": number, "label": string}},
    ...
  ]
}}

Rules:
- Only include individual sets (e.g., ["Coffee"]) — do NOT include intersections like ["Coffee", "Tea"].
- If a group is mentioned that does not belong to any of the clear sets (e.g., "6 do not belong to any group", "4 people don't like any of..."), treat it as a separate individual set named "None".
  - Use "Out" in the "sets" array.
  - Use "Out" as the truncated identifier for labeling. For example: "Out(4)".
- Labeling:
  - If the prompt gives a number (e.g., "15 people like coffee", "6 do not belong to any"), truncate the set name to a maximum of 3 characters (e.g., "Cof" for "Coffee") and format the label as "Cof(15)".
  - If no number is given, use the full set name as the label.
  - Always return the label as a plain string — no markdown, no quotation marks inside the value.
- Always use the exact names from the prompt for the "sets" field — do not truncate or modify them (except "None" as specified).
- Return strict, valid JSON only — no trailing commas, no explanations, no markdown code blocks.
""")


# 4. Chain: From prompt and numSets → unique Venn set regions
unique_sets_chain = (
    venn_unique_sets_prompt
    | chat.with_structured_output(VennUniqueSetsOutput)
    | RunnableLambda(lambda output: _log_and_return_unique_sets(output))
)


def _log_and_return_unique_sets(output: VennUniqueSetsOutput) -> VennUniqueSetsOutput:
    import json
    print("🟢 [Venn Unique Sets Output]:", json.dumps(output.dict(), indent=2))
    return output
