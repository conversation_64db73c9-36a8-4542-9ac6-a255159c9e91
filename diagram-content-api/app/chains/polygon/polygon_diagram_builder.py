from typing import List, Optional, Dict
from app.models.diagram_types import PolygonDiagramParams
from app.chains.polygon.extract_shape_intent import ShapeIntent
from app.chains.polygon.extract_polygon_labels import PolygonLabelOutput


def generate_regular_polygon(
    sides: int,
    radius: float = 0.4,
    center: Dict[str, float] = {"x": 0.5, "y": 0.5}
) -> List[Dict[str, float]]:
    import math

    angle_step = (2 * math.pi) / sides
    points = []
    for i in range(sides):
        angle = i * angle_step - math.pi / 2
        x = round(center["x"] + radius * math.cos(angle), 4)
        y = round(center["y"] + radius * math.sin(angle), 4)
        points.append({"x": x, "y": y})
    return points


def generate_line_labels(point_labels: List[str]) -> List[str]:
    num_labels = len(point_labels)
    return [f"{point_labels[i]}{point_labels[(i + 1) % num_labels]}" for i in range(num_labels)]


async def build_polygon_diagram(
    user_prompt: str,
    shape: ShapeIntent,
    labels: Optional[PolygonLabelOutput] = None
) -> PolygonDiagramParams:
    sides = max(3, min(20, shape.numberOfSides))
    points = generate_regular_polygon(sides)

    diagram = PolygonDiagramParams(
        diagramType="polygon",
        width=1,
        height=1,
        points=points,
        fill="#f5f5f5",
        stroke="black",
        strokeWidth=1,
    )

    if shape.shouldLabelPoints and labels and labels.pointLabels and len(labels.pointLabels) == len(points):
        diagram.pointLabels = labels.pointLabels

    if shape.shouldLabelLines:
        num_sides = len(points)

        if labels and labels.lineLabels and len(labels.lineLabels) == num_sides:
            diagram.lineLabels = labels.lineLabels

        elif labels and labels.lineLabels and len(labels.lineLabels) > 0:
            first = labels.lineLabels[0]
            all_same = all(val == first for val in labels.lineLabels)

            if len(labels.lineLabels) == 1 or all_same:
                diagram.lineLabels = [first] * num_sides

        elif diagram.pointLabels and len(diagram.pointLabels) == num_sides:
            diagram.lineLabels = generate_line_labels(diagram.pointLabels)

    return diagram
