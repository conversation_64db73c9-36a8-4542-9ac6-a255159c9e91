# Advanced Graph Component Documentation

## Overview

The Advanced Graph Component is a comprehensive, feature-rich graphing solution that supports both static plotting and interactive data visualization. It leverages multiple high-quality libraries including Plotly.js, Visx, and D3.js to provide top-tier graphing capabilities with animations, interactivity, and customization.

## Features

### 🎯 Core Capabilities
- **Dual Mode Support**: Plot mode (static) and Interactive mode (with controls)
- **Multiple Graph Types**: Line, scatter, bar, area, spline, and step charts
- **Multi-Series Support**: Overlay multiple datasets with different colors and styles
- **Advanced Animations**: Smooth transitions, staggered animations, and custom easing
- **Interactive Controls**: Zoom, pan, selection, brushing, and hover effects
- **Responsive Design**: Automatically adapts to container size
- **Theme Support**: Light, dark, and custom themes

### 📊 Graph Types Supported
1. **Line Charts**: Standard line graphs with customizable stroke width and colors
2. **Spline Charts**: Smooth curved lines using cardinal interpolation
3. **Step Charts**: Step-wise line charts for discrete data
4. **Scatter Plots**: Point-based charts with variable sizes and colors
5. **Bar Charts**: Vertical bar charts with customizable widths
6. **Area Charts**: Filled area charts with transparency support

### 🎮 Interactive Features
- **Zoom & Pan**: Mouse wheel zoom and click-drag panning
- **Selection**: Click and drag to select data points
- **Brushing**: Advanced selection with brush tool
- **Hover Effects**: Interactive tooltips and point highlighting
- **Crosshair**: Precision cursor for data reading
- **Real-time Controls**: Toggle series visibility, change colors, modify chart types

### 🎨 Customization Options
- **Colors**: Full color customization for series, backgrounds, and UI elements
- **Styling**: Stroke width, fill opacity, point sizes, and more
- **Axes**: Custom labels, tick formatting, grid visibility, and ranges
- **Legend**: Configurable position, orientation, and styling
- **Tooltips**: Custom formatting and styling
- **Margins**: Adjustable spacing around the plot area

## Usage

### Basic Usage

```tsx
import GraphComponent from './components/diagrams/GraphComponent';
import { GraphDiagramParams } from './diagramTypes';

const basicGraph: GraphDiagramParams = {
  diagramType: "graph",
  mode: "interactive",
  series: [
    {
      id: "data1",
      name: "Sample Data",
      data: [
        { x: 0, y: 0 },
        { x: 1, y: 1 },
        { x: 2, y: 4 },
        { x: 3, y: 9 },
      ],
      type: "line",
      color: "#0077ff",
    },
  ],
  xAxis: { label: "X Axis", gridVisible: true },
  yAxis: { label: "Y Axis", gridVisible: true },
  interaction: { zoom: true, pan: true, hover: true },
};

function MyComponent() {
  return <GraphComponent params={basicGraph} />;
}
```

### Advanced Multi-Series Example

```tsx
const advancedGraph: GraphDiagramParams = {
  diagramType: "graph",
  mode: "interactive",
  width: 800,
  height: 500,
  series: [
    {
      id: "sine",
      name: "Sine Wave",
      data: Array.from({ length: 100 }, (_, i) => ({
        x: i * 0.1,
        y: Math.sin(i * 0.1),
      })),
      type: "spline",
      color: "#0077ff",
      strokeWidth: 3,
      animated: true,
      animationDuration: 2000,
    },
    {
      id: "cosine",
      name: "Cosine Wave",
      data: Array.from({ length: 100 }, (_, i) => ({
        x: i * 0.1,
        y: Math.cos(i * 0.1),
      })),
      type: "spline",
      color: "#ff3366",
      strokeWidth: 3,
      animated: true,
      animationDuration: 2000,
      animationDelay: 500,
    },
  ],
  xAxis: {
    label: "X (radians)",
    gridVisible: true,
    gridColor: "#e0e0e0",
    gridOpacity: 0.5,
  },
  yAxis: {
    label: "Y",
    gridVisible: true,
    min: -1.5,
    max: 1.5,
  },
  legend: {
    visible: true,
    position: "top-right",
    backgroundColor: "rgba(255, 255, 255, 0.9)",
  },
  tooltip: {
    enabled: true,
    format: "(%{x:.2f}, %{y:.2f})",
  },
  interaction: {
    zoom: true,
    pan: true,
    hover: true,
    crosshair: true,
  },
  animation: {
    enabled: true,
    duration: 2000,
    easing: "ease-out",
    stagger: 500,
  },
};
```

## API Reference

### GraphDiagramParams Interface

```typescript
interface GraphDiagramParams {
  diagramType: "graph";
  mode: 'plot' | 'interactive';
  width?: number;
  height?: number;
  series: GraphSeries[];
  xAxis?: GraphAxis;
  yAxis?: GraphAxis;
  legend?: GraphLegend;
  tooltip?: GraphTooltip;
  interaction?: GraphInteraction;
  animation?: GraphAnimation;
  backgroundColor?: string;
  margin?: { top?: number; right?: number; bottom?: number; left?: number };
  theme?: 'light' | 'dark' | 'custom';
  customTheme?: CustomTheme;
}
```

### GraphSeries Interface

```typescript
interface GraphSeries {
  id: string;
  name: string;
  data: DataPoint[];
  type: 'line' | 'scatter' | 'bar' | 'area' | 'spline' | 'step';
  color?: string;
  strokeWidth?: number;
  fillOpacity?: number;
  visible?: boolean;
  animated?: boolean;
  animationDuration?: number;
  animationDelay?: number;
}
```

### DataPoint Interface

```typescript
interface DataPoint {
  x: number;
  y: number;
  label?: string;
  color?: string;
  size?: number;
}
```

## Integration with Existing System

### Diagram Generator Integration

The graph component is fully integrated with the existing AI-powered diagram generator. Users can request graphs using natural language:

**Example prompts that generate graphs:**
- "Create a line chart showing sales data over 12 months"
- "Plot a sine wave from 0 to 2π"
- "Show a scatter plot with random data points"
- "Generate a bar chart comparing quarterly revenue"
- "Create an interactive graph with multiple overlaid functions"

### CompositeDiagram Integration

The graph component works seamlessly with the existing `CompositeDiagram` system:

```tsx
// Automatically handles graph types
<CompositeDiagram definition={graphDefinition} />

// Works with transitions
<CompositeDiagramWithTransitions 
  definition={graphDefinition}
  transitionType="fade"
  animationType="infinitePulse"
/>
```

### AnimatedDiagramSequence Support

Graphs can be part of animated sequences:

```tsx
const sequence = [
  { ...barChartDefinition, duration: 3000 },
  { ...lineChartDefinition, duration: 3000 },
  { ...scatterPlotDefinition, duration: 3000 },
];

<AnimatedDiagramSequence sequence={sequence} loop={true} />
```

## Library Architecture

### Intelligent Library Selection

The component automatically chooses the best rendering library based on complexity:

- **Plotly.js**: Used for complex interactions, animations, and multiple series
- **Visx**: Used for simpler, more customizable graphs with better performance
- **D3.js**: Underlying foundation for custom visualizations

### Performance Optimizations

- Automatic library selection based on data complexity
- Efficient re-rendering with React.memo and useMemo
- Optimized animation frames for smooth transitions
- Lazy loading of heavy libraries

## Examples and Demos

Visit `/graph-examples` in the application to see comprehensive examples including:

1. **Interactive Multi-Series**: Trigonometric functions with zoom/pan
2. **Static Bar Chart**: Monthly sales with bounce animations
3. **Scatter Plot**: Random data with variable point sizes
4. **Area Chart**: Revenue growth with smooth transitions

## Best Practices

### Performance
- Use plot mode for static displays to reduce bundle size
- Limit data points to 1000-2000 for smooth interactions
- Use animation sparingly for better performance

### Accessibility
- Always provide axis labels
- Use sufficient color contrast
- Include alternative text descriptions

### User Experience
- Enable tooltips for data exploration
- Provide zoom/reset controls for complex data
- Use consistent color schemes across related charts

## Troubleshooting

### Common Issues

1. **Missing Dependencies**: Ensure all Visx and Plotly packages are installed
2. **Performance Issues**: Reduce data points or disable animations
3. **Styling Conflicts**: Use the theme system instead of custom CSS

### Debug Mode

Enable debug logging by setting:
```typescript
const params = {
  ...graphParams,
  debug: true, // Enables console logging
};
```

## Future Enhancements

- 3D plotting capabilities
- Real-time data streaming
- Advanced statistical overlays
- Export to various formats (PNG, SVG, PDF)
- Collaborative editing features
- Voice control integration
