{"name": "diagram-builder", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@langchain/core": "^0.3.37", "@langchain/openai": "^0.4.2", "@tailwindcss/vite": "^4.0.3", "@types/animejs": "^3.1.13", "@types/plotly.js": "^3.0.0", "@types/venn": "^0.2.7", "@visx/axis": "^3.12.0", "@visx/brush": "^3.12.0", "@visx/clip-path": "^3.12.0", "@visx/curve": "^3.12.0", "@visx/event": "^3.12.0", "@visx/gradient": "^3.12.0", "@visx/grid": "^3.12.0", "@visx/group": "^3.12.0", "@visx/responsive": "^3.12.0", "@visx/scale": "^3.12.0", "@visx/shape": "^3.12.0", "@visx/tooltip": "^3.12.0", "@visx/visx": "^3.12.0", "@visx/zoom": "^3.12.0", "animejs": "^4.0.2", "d3": "^7.9.0", "d3-scale": "^4.0.2", "d3-selection": "^3.0.0", "d3-shape": "^3.2.0", "d3-zoom": "^3.0.0", "flubber": "^0.4.2", "fmin": "^0.0.4", "framer-motion": "^12.9.2", "langchain": "^0.3.19", "lucide-react": "^0.512.0", "mathjs": "^14.4.0", "plotly.js-dist-min": "^3.0.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router": "^7.1.5", "recharts": "^2.15.3", "tailwindcss": "^4.0.3", "venn.js": "^0.2.20", "victory": "^37.3.6", "zod": "^3.24.2"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/d3": "^7.4.3", "@types/d3-scale": "^4.0.9", "@types/d3-selection": "^3.0.11", "@types/d3-shape": "^3.1.7", "@types/d3-zoom": "^3.0.8", "@types/flubber": "^0.4.0", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react-swc": "^3.5.0", "eslint": "^9.17.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "typescript": "~5.6.2", "typescript-eslint": "^8.18.2", "vite": "^6.0.5"}}