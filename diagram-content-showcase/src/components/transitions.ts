const transitions = {
    fade: {
        initial: { opacity: 0 },
        animate: { opacity: 1 },
        exit: { opacity: 0 },
        transition: { duration: 0.6, ease: "easeInOut" },
    },
    zoom: {
        initial: { opacity: 0, scale: 1.05, filter: "blur(4px)" },
        animate: { opacity: 1, scale: 1, filter: "blur(0px)" },
        exit: { opacity: 0, scale: 0.95, filter: "blur(4px)" },
        transition: { duration: 0.6, ease: "easeInOut" },
    },
    slide: {
        initial: { x: 100, opacity: 0 },
        animate: { x: 0, opacity: 1 },
        exit: { x: -100, opacity: 0 },
        transition: { duration: 0.5, ease: "easeOut" },
    },
    flip: {
        initial: { rotateY: 90, opacity: 0 },
        animate: { rotateY: 0, opacity: 1 },
        exit: { rotateY: -90, opacity: 0 },
        transition: { duration: 0.7, ease: "easeInOut" },
    },
    drop: {
        initial: { y: -200, scale: 0.8, opacity: 0 },
        animate: {
            y: 0,
            scale: 1,
            opacity: 1,
            transition: {
                type: "spring",
                stiffness: 100,
                damping: 10,
            },
        },
        exit: {
            y: 200,
            scale: 0.8,
            opacity: 0,
            transition: { duration: 0.5, ease: "easeIn" },
        },
        transition: {},
    }
    ,
    rotate: {
        initial: { rotate: 90, scale: 0.5, opacity: 0 },
        animate: { rotate: 0, scale: 1, opacity: 1 },
        exit: { rotate: -90, scale: 0.5, opacity: 0 },
        transition: { duration: 0.7, ease: "easeInOut" },
    },
    warp: {
        initial: { scale: 0.2, filter: "blur(12px)", opacity: 0 },
        animate: { scale: 1, filter: "blur(0px)", opacity: 1 },
        exit: { scale: 0.2, filter: "blur(12px)", opacity: 0 },
        transition: { duration: 0.8, ease: "easeInOut" },
    },
    diagonal: {
        initial: { x: 100, y: 100, opacity: 0 },
        animate: { x: 0, y: 0, opacity: 1 },
        exit: { x: -100, y: -100, opacity: 0 },
        transition: { duration: 0.7, ease: [0.68, -0.55, 0.27, 1.55] },
    },
    distortFade: {
        initial: {
            opacity: 0,
            scale: 0.98,
            filter: "blur(6px)",
            rotateZ: 1,
        },
        animate: {
            opacity: 1,
            scale: 1,
            filter: "blur(0px)",
            rotateZ: 0,
            transition: { duration: 0.6, ease: "easeInOut" },
        },
        exit: {
            opacity: 0,
            scale: 1.02,
            filter: "blur(8px)",
            rotateZ: -1,
            transition: { duration: 0.5, ease: "easeInOut" },
        },
        transition: {},
    },
    ripple: {
        initial: { scale: 0.3, opacity: 0, filter: "blur(6px)" },
        animate: {
            scale: 1.1,
            opacity: 1,
            filter: "blur(0px)",
            transition: { type: "spring", stiffness: 200, damping: 5 },
        },
        exit: {
            scale: 0.3,
            opacity: 0,
            filter: "blur(6px)",
            transition: { duration: 0.4 },
        }, transition: {},
    },

    // 🔴 Glitchy Slide
    glitch: {
        initial: { x: 50, opacity: 0, filter: "contrast(0.5) saturate(2)" },
        animate: {
            x: 0,
            opacity: 1,
            filter: "none",
            transition: { duration: 0.6, ease: "easeInOut" },
        },
        exit: {
            x: -50,
            opacity: 0,
            filter: "contrast(0.5) saturate(2)",
            transition: { duration: 0.4 },
        }, transition: {},
    },

    // 💥 Explode
    explode: {
        initial: {
            scale: 0,
            opacity: 0,
            rotate: 10,
            originX: 0.5,
            originY: 0.5,
        },
        animate: {
            scale: 1,
            opacity: 1,
            rotate: 0,
            transition: { type: "spring", stiffness: 120, damping: 10 },
        },
        exit: {
            scale: 0.7,
            opacity: 0,
            transition: { duration: 0.3 },
        }, transition: {},
    },

    // 🪐 Orbit
    orbit: {
        initial: { rotate: 360, scale: 0.4, opacity: 0 },
        animate: {
            rotate: 0,
            scale: 1,
            opacity: 1,
            transition: { duration: 0.7, ease: "easeOut" },
        },
        exit: {
            rotate: -360,
            scale: 0.4,
            opacity: 0,
            transition: { duration: 0.5, ease: "easeIn" },
        }, transition: {},
    },
};

export default transitions;