import React, { useEffect, useRef, useState } from "react";
import { Pause, Play, RotateCcw } from "lucide-react";
import CompositeDiagramWithTranstions from "./CompositeDiagramWithTransitions";
import { AnimationType, DiagramDefinition, TransitionType } from "../diagramTypes";

interface AnimatedDiagramSequenceProps {
  sequence: TimedDiagramDefinition[];
  loop?: boolean;
  playOnRender?: boolean; // new prop
}

type TimedDiagramDefinition = DiagramDefinition & {
  duration?: number;
  audioUrl?: string;
  transitionType?: TransitionType;
  animationType?: AnimationType;
};

const CompleteAnimatedDiagramSequence: React.FC<AnimatedDiagramSequenceProps> = ({
  sequence,
  loop = false,
  playOnRender = false, // default to true
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [diagramDuration, setDiagramDuration] = useState<number | null>(null);
  const [playing, setPlaying] = useState(playOnRender);

  const audioRef = useRef<HTMLAudioElement | null>(null);
  const audioPlayTimeout = useRef<ReturnType<typeof setTimeout> | null>(null);
  const audioStopTimeout = useRef<ReturnType<typeof setTimeout> | null>(null);
  const diagramTimer = useRef<ReturnType<typeof setTimeout> | null>(null);

  const currentDiagram = sequence[currentIndex];

  // Load audio and calculate diagram duration
  useEffect(() => {
    if (currentDiagram.audioUrl) {
      const audio = new Audio(currentDiagram.audioUrl);
      audioRef.current = audio;

      const handleMetadata = () => {
        const audioDuration = audio.duration;
        const totalDuration = (audioDuration + 2) * 1000;
        setDiagramDuration(totalDuration);

        if (playing) {
          audioPlayTimeout.current = setTimeout(() => {
            audio.play();
          }, 1000);

          audioStopTimeout.current = setTimeout(() => {
            audio.pause();
            audio.currentTime = 0;
          }, totalDuration - 1000);
        }
      };

      audio.addEventListener("loadedmetadata", handleMetadata);
      audio.load();

      return () => {
        audio.removeEventListener("loadedmetadata", handleMetadata);
        audio.pause();
        audio.currentTime = 0;
      };
    } else {
      setDiagramDuration(currentDiagram.duration || 4000);
    }

    return () => {
      if (audioPlayTimeout.current) clearTimeout(audioPlayTimeout.current);
      if (audioStopTimeout.current) clearTimeout(audioStopTimeout.current);
    };
  }, [currentDiagram, playing]);

  // Advance to next diagram
  useEffect(() => {
    if (diagramDuration !== null && playing) {
      diagramTimer.current = setTimeout(() => {
        if (currentIndex < sequence.length - 1) {
          setCurrentIndex(currentIndex + 1);
        } else if (loop) {
          setCurrentIndex(0);
        }
      }, diagramDuration);

      return () => clearTimeout(diagramTimer.current!);
    }
  }, [diagramDuration, currentIndex, sequence.length, loop, playing]);

  // Control functions
  const pause = () => {
    setPlaying(false);
    if (diagramTimer.current) clearTimeout(diagramTimer.current);
    if (audioPlayTimeout.current) clearTimeout(audioPlayTimeout.current);
    if (audioStopTimeout.current) clearTimeout(audioStopTimeout.current);
    if (audioRef.current) audioRef.current.pause();
  };

  const resume = () => {
    setPlaying(true);
    if (audioRef.current && audioRef.current.currentTime > 0) {
      audioRef.current.play();
    }
  };

  const restart = () => {
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
    }
    if (diagramTimer.current) clearTimeout(diagramTimer.current);
    if (audioPlayTimeout.current) clearTimeout(audioPlayTimeout.current);
    if (audioStopTimeout.current) clearTimeout(audioStopTimeout.current);
    setCurrentIndex(0);
    setPlaying(true);
  };

  return (
    <div className="flex flex-col justify-center items-center w-full h-full">
      <CompositeDiagramWithTranstions
        definition={currentDiagram}
        animationType={currentDiagram.animationType}
        transitionType={currentDiagram.transitionType}
      />
      <div className="mt-4 flex gap-2 justify-center">
        {playing ? (
          <button
            onClick={pause}
            className="flex cursor-pointer items-center gap-1 px-3 py-1.5 bg-yellow-500 hover:bg-yellow-600 text-white rounded shadow transition"
          >
            <Pause size={16} />
          </button>
        ) : (
          <button
            onClick={resume}
            className="flex cursor-pointer items-center gap-1 px-3 py-1.5 bg-green-500 hover:bg-green-600 text-white rounded shadow transition"
          >
            <Play size={16} />
          </button>
        )}
        <button
          onClick={restart}
          className="flex cursor-pointer items-center gap-1 px-3 py-1.5 bg-blue-500 hover:bg-blue-600 text-white rounded shadow transition"
        >
          <RotateCcw size={16} />
        </button>
      </div>
    </div>
  );
};

export default CompleteAnimatedDiagramSequence;
