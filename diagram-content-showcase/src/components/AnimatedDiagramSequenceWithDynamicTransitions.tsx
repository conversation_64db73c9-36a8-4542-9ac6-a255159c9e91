import React, { useEffect, useState } from "react";
import CompositeDiagramWithTranstions from "./CompositeDiagramWithTransitions";
import { DiagramDefinition, TransitionType } from "../diagramTypes";

interface AnimatedDiagramSequenceProps {
  sequence: TimedDiagramDefinition[];
  loop?: boolean;
}

// Add this in diagramTypes.ts or a separate types file


type TimedDiagramDefinition = DiagramDefinition & {
  duration: number;
  transitionType?: TransitionType;
};

const AnimatedDiagramSequenceWithDynamicTransitions: React.FC<AnimatedDiagramSequenceProps> = ({
  sequence,
  loop = false,
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    const current = sequence[currentIndex];
    const timer = setTimeout(() => {
      if (currentIndex < sequence.length - 1) {
        setCurrentIndex(currentIndex + 1);
      } else if (loop) {
        setCurrentIndex(0);
      }
    }, current.duration);

    return () => clearTimeout(timer);
  }, [currentIndex, sequence, loop]);

  const current = sequence[currentIndex];

  return (
    <div className="flex justify-center items-center w-full h-full">
      <CompositeDiagramWithTranstions
        definition={current}
        transitionType={current.transitionType}
      />
    </div>
  );
};

export default AnimatedDiagramSequenceWithDynamicTransitions;
