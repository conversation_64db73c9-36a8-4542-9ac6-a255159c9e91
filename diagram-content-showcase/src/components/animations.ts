const animations = {
  infiniteRotate: {
    animate: {
      rotate: [0, 360],
      transition: {
        duration: 2, // seconds for full rotation
        repeat: Infinity,
        ease: "linear",
      },
    },
  },
  infiniteBounce: {
    animate: {
      y: [0, -20, 0],
      transition: {
        duration: 1,
        repeat: Infinity,
        repeatType: "loop",
        ease: "easeInOut",
      },
    },
  },
  infinitePulse: {
    animate: {
      scale: [1, 0.9, 1],
      transition: {
        duration: 1,
        repeat: Infinity,
        repeatType: "loop",
        ease: "easeInOut",
      },
    },
  },
  infiniteWiggle: {
    animate: {
      rotate: [-5, 5, -5],
      transition: {
        duration: 0.5,
        repeat: Infinity,
        repeatType: "loop",
        ease: "easeInOut",
      },
    },
  },
  infiniteGlow: {
    animate: {
      filter: ["drop-shadow(0 0 4px #fff)", "drop-shadow(0 0 8px #fff)", "drop-shadow(0 0 4px #fff)"],
      transition: {
        duration: 2,
        repeat: Infinity,
        repeatType: "loop",
        ease: "easeInOut",
      },
    },
  },
};

export default animations;
