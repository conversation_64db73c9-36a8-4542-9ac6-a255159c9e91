// AnimatedDiagramSequence.tsx
import React, { useEffect, useState } from "react";
import CompositeDiagram from "./CompositeDiagram";
import { DiagramDefinition } from "../diagramTypes";

// Extend the definition to include a duration for how long to show this state
export type TimedDiagramDefinition = DiagramDefinition & {
  duration: number; // in milliseconds
};

interface AnimatedDiagramSequenceProps {
  sequence: TimedDiagramDefinition[];
  loop?: boolean;
}

const AnimatedDiagramSequence: React.FC<AnimatedDiagramSequenceProps> = ({
  sequence,
  loop = false,
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    const current = sequence[currentIndex];
    const timer = setTimeout(() => {
      if (currentIndex < sequence.length - 1) {
        setCurrentIndex(currentIndex + 1);
      } else if (loop) {
        setCurrentIndex(0);
      }
    }, current.duration);

    return () => clearTimeout(timer);
  }, [currentIndex, sequence, loop]);

  return (
    <div className="flex justify-center items-center w-full h-full">
      <CompositeDiagram definition={sequence[currentIndex]} />
    </div>
  );
};

export default AnimatedDiagramSequence;
