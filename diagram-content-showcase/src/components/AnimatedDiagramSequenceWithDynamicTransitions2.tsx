import React, { useEffect, useState } from "react";
import CompositeDiagramWithTransitions from "./CompositeDiagramWithTransitions";
import { AnimationType, DiagramDefinition, TransitionType } from "../diagramTypes";
import { AnimatePresence, motion } from "framer-motion";
import transitions from "./transitions";

interface AnimatedDiagramSequenceProps {
  sequence: TimedDiagramDefinition[];
  loop?: boolean;
}

// Add this in diagramTypes.ts or a separate types file


type TimedDiagramDefinition = DiagramDefinition & {
  duration: number;
  transitionType?: TransitionType;
  animationType?: AnimationType;
};

const AnimatedDiagramSequenceWithDynamicTransitions: React.FC<AnimatedDiagramSequenceProps> = ({
  sequence,
  loop = false,
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    const current = sequence[currentIndex];
    const timer = setTimeout(() => {
      if (currentIndex < sequence.length - 1) {
        setCurrentIndex(currentIndex + 1);
      } else if (loop) {
        setCurrentIndex(0);
      }
    }, current.duration);

    return () => clearTimeout(timer);
  }, [currentIndex, sequence, loop]);


  const current = sequence[currentIndex];
  const prev = (currentIndex - 1 < 0) ? sequence[currentIndex - 1] : sequence[currentIndex];

  return (
    <AnimatePresence mode="wait">
      <motion.div
        key={JSON.stringify(current)}
        initial={transitions[prev?.transitionType as TransitionType]?.initial}
        animate={transitions[prev?.transitionType as TransitionType]?.animate}
        exit={transitions[current?.transitionType as TransitionType]?.initial}
        transition={transitions[current?.transitionType as TransitionType]?.initial}
      >
        <CompositeDiagramWithTransitions
          definition={current}
          animationType={current.animationType}
          // transitionType={current.transitionType}
        />
      </motion.div>
    </AnimatePresence>

  );
};

export default AnimatedDiagramSequenceWithDynamicTransitions;
