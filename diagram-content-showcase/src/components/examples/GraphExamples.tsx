import React, { useState } from 'react';
import { motion } from 'framer-motion';
import GraphComponent from '../diagrams/GraphComponent';
import { GraphDiagramParams } from '../../diagramTypes';

const GraphExamples: React.FC = () => {
  const [selectedExample, setSelectedExample] = useState<string>('interactive-multi');

  // Example 1: Interactive Multi-Series Line Chart
  const interactiveMultiSeries: GraphDiagramParams = {
    diagramType: "graph",
    mode: "interactive",
    width: 800,
    height: 500,
    series: [
      {
        id: "sine",
        name: "Sine Wave",
        data: Array.from({ length: 100 }, (_, i) => ({
          x: i * 0.1,
          y: Math.sin(i * 0.1),
        })),
        type: "spline",
        color: "#0077ff",
        strokeWidth: 3,
        animated: true,
        animationDuration: 2000,
        animationDelay: 0,
      },
      {
        id: "cosine",
        name: "Cosine Wave",
        data: Array.from({ length: 100 }, (_, i) => ({
          x: i * 0.1,
          y: Math.cos(i * 0.1),
        })),
        type: "spline",
        color: "#ff3366",
        strokeWidth: 3,
        animated: true,
        animationDuration: 2000,
        animationDelay: 500,
      },
      {
        id: "tangent",
        name: "Tangent Wave (Clipped)",
        data: Array.from({ length: 100 }, (_, i) => {
          const x = i * 0.1;
          const y = Math.tan(x);
          return {
            x,
            y: Math.max(-3, Math.min(3, y)), // Clip extreme values
          };
        }),
        type: "line",
        color: "#00cc88",
        strokeWidth: 2,
        animated: true,
        animationDuration: 2000,
        animationDelay: 1000,
      },
    ],
    xAxis: {
      label: "X (radians)",
      gridVisible: true,
      gridColor: "#e0e0e0",
      gridOpacity: 0.5,
      tickCount: 10,
    },
    yAxis: {
      label: "Y",
      gridVisible: true,
      gridColor: "#e0e0e0",
      gridOpacity: 0.5,
      min: -3,
      max: 3,
    },
    legend: {
      visible: true,
      position: "top-right",
      backgroundColor: "rgba(255, 255, 255, 0.9)",
      borderColor: "#ccc",
    },
    tooltip: {
      enabled: true,
      format: "(%{x:.2f}, %{y:.2f})",
      backgroundColor: "rgba(0, 0, 0, 0.8)",
      textColor: "white",
    },
    interaction: {
      zoom: true,
      pan: true,
      hover: true,
      crosshair: true,
    },
    animation: {
      enabled: true,
      duration: 2000,
      easing: "easeOut",
      stagger: 500,
    },
    backgroundColor: "#fafafa",
    theme: "light",
  };

  // Example 2: Static Bar Chart
  const staticBarChart: GraphDiagramParams = {
    diagramType: "graph",
    mode: "plot",
    width: 600,
    height: 400,
    series: [
      {
        id: "sales",
        name: "Monthly Sales",
        data: [
          { x: 1, y: 120, label: "Jan" },
          { x: 2, y: 150, label: "Feb" },
          { x: 3, y: 180, label: "Mar" },
          { x: 4, y: 140, label: "Apr" },
          { x: 5, y: 200, label: "May" },
          { x: 6, y: 170, label: "Jun" },
        ],
        type: "bar",
        color: "#4f46e5",
        animated: true,
        animationDuration: 1500,
      },
    ],
    xAxis: {
      label: "Month",
      tickFormat: "d",
      gridVisible: false,
    },
    yAxis: {
      label: "Sales ($k)",
      gridVisible: true,
      gridColor: "#e5e7eb",
      min: 0,
      max: 250,
    },
    legend: {
      visible: false,
    },
    tooltip: {
      enabled: false,
    },
    animation: {
      enabled: true,
      duration: 1500,
      easing: "backOut",
    },
    backgroundColor: "white",
    theme: "light",
  };

  // Example 3: Scatter Plot with Different Sizes
  const scatterPlot: GraphDiagramParams = {
    diagramType: "graph",
    mode: "interactive",
    width: 700,
    height: 450,
    series: [
      {
        id: "dataset1",
        name: "Dataset A",
        data: Array.from({ length: 50 }, () => ({
          x: Math.random() * 10,
          y: Math.random() * 10,
          size: Math.random() * 10 + 3,
        })),
        type: "scatter",
        color: "#8b5cf6",
        animated: true,
        animationDuration: 1000,
      },
      {
        id: "dataset2",
        name: "Dataset B",
        data: Array.from({ length: 30 }, () => ({
          x: Math.random() * 10 + 5,
          y: Math.random() * 10 + 5,
          size: Math.random() * 8 + 4,
        })),
        type: "scatter",
        color: "#f59e0b",
        animated: true,
        animationDuration: 1000,
        animationDelay: 300,
      },
    ],
    xAxis: {
      label: "X Value",
      gridVisible: true,
      min: 0,
      max: 15,
    },
    yAxis: {
      label: "Y Value",
      gridVisible: true,
      min: 0,
      max: 15,
    },
    legend: {
      visible: true,
      position: "bottom",
      orientation: "horizontal",
    },
    tooltip: {
      enabled: true,
      format: "Point: (%{x:.1f}, %{y:.1f})",
    },
    interaction: {
      zoom: true,
      pan: true,
      select: true,
      hover: true,
    },
    animation: {
      enabled: true,
      duration: 1000,
      easing: "easeOut",
      stagger: 300,
    },
  };

  // Example 4: Area Chart
  const areaChart: GraphDiagramParams = {
    diagramType: "graph",
    mode: "interactive",
    width: 800,
    height: 400,
    series: [
      {
        id: "area1",
        name: "Revenue",
        data: [
          { x: 0, y: 0 },
          { x: 1, y: 20 },
          { x: 2, y: 45 },
          { x: 3, y: 30 },
          { x: 4, y: 60 },
          { x: 5, y: 80 },
          { x: 6, y: 70 },
          { x: 7, y: 90 },
          { x: 8, y: 85 },
          { x: 9, y: 100 },
        ],
        type: "area",
        color: "#10b981",
        fillOpacity: 0.3,
        strokeWidth: 3,
        animated: true,
        animationDuration: 2000,
      },
    ],
    xAxis: {
      label: "Time (months)",
      gridVisible: true,
      gridColor: "#f3f4f6",
    },
    yAxis: {
      label: "Revenue ($k)",
      gridVisible: true,
      gridColor: "#f3f4f6",
      min: 0,
    },
    legend: {
      visible: true,
      position: "top-left",
    },
    tooltip: {
      enabled: true,
      format: "Month %{x}: $%{y}k",
    },
    interaction: {
      zoom: true,
      pan: true,
      hover: true,
    },
    animation: {
      enabled: true,
      duration: 2000,
      easing: "easeInOut",
    },
    backgroundColor: "#ffffff",
  };

  const examples = {
    'interactive-multi': { data: interactiveMultiSeries, title: 'Interactive Multi-Series (Trigonometric Functions)' },
    'static-bar': { data: staticBarChart, title: 'Static Bar Chart (Monthly Sales)' },
    'scatter': { data: scatterPlot, title: 'Interactive Scatter Plot (Random Data)' },
    'area': { data: areaChart, title: 'Interactive Area Chart (Revenue Growth)' },
  };

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <h1 className="text-3xl font-bold text-gray-800 mb-6">Advanced Graph Component Examples</h1>
      
      {/* Example Selector */}
      <div className="mb-6">
        <div className="flex flex-wrap gap-2">
          {Object.entries(examples).map(([key, example]) => (
            <button
              key={key}
              onClick={() => setSelectedExample(key)}
              className={`px-4 py-2 rounded-lg transition-colors ${
                selectedExample === key
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-200 hover:bg-gray-300 text-gray-700'
              }`}
            >
              {example.title}
            </button>
          ))}
        </div>
      </div>

      {/* Selected Example */}
      <motion.div
        key={selectedExample}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="bg-white rounded-lg shadow-lg p-6"
      >
        <h2 className="text-xl font-semibold text-gray-700 mb-4">
          {examples[selectedExample as keyof typeof examples].title}
        </h2>
        
        <div className="border border-gray-200 rounded-lg overflow-hidden">
          <GraphComponent 
            params={examples[selectedExample as keyof typeof examples].data}
          />
        </div>

        {/* Features Description */}
        <div className="mt-4 p-4 bg-gray-50 rounded-lg">
          <h3 className="font-medium text-gray-700 mb-2">Features Demonstrated:</h3>
          <ul className="text-sm text-gray-600 space-y-1">
            {selectedExample === 'interactive-multi' && (
              <>
                <li>• Multiple overlaid series with different colors</li>
                <li>• Smooth spline and line interpolation</li>
                <li>• Interactive zoom, pan, and crosshair</li>
                <li>• Staggered animations with different delays</li>
                <li>• Custom tooltip formatting</li>
                <li>• Grid customization and axis labeling</li>
              </>
            )}
            {selectedExample === 'static-bar' && (
              <>
                <li>• Static plot mode (no interactive controls)</li>
                <li>• Bar chart with bounce animation</li>
                <li>• Custom color scheme</li>
                <li>• Disabled tooltip and legend</li>
                <li>• Fixed axis ranges</li>
              </>
            )}
            {selectedExample === 'scatter' && (
              <>
                <li>• Variable point sizes</li>
                <li>• Multiple datasets with different colors</li>
                <li>• Selection and hover interactions</li>
                <li>• Horizontal legend positioning</li>
                <li>• Staggered series animations</li>
              </>
            )}
            {selectedExample === 'area' && (
              <>
                <li>• Filled area chart with transparency</li>
                <li>• Smooth area transitions</li>
                <li>• Custom fill opacity</li>
                <li>• Interactive zoom and pan</li>
                <li>• Custom tooltip format</li>
              </>
            )}
          </ul>
        </div>
      </motion.div>

      {/* Usage Instructions */}
      <div className="mt-8 p-6 bg-blue-50 rounded-lg">
        <h3 className="text-lg font-semibold text-blue-800 mb-3">Usage Instructions</h3>
        <div className="text-sm text-blue-700 space-y-2">
          <p><strong>Interactive Mode:</strong> Use mouse wheel to zoom, click and drag to pan, hover for tooltips.</p>
          <p><strong>Plot Mode:</strong> Static display with animations but no user interactions.</p>
          <p><strong>Controls:</strong> In interactive mode, click the settings icon (⚙️) for advanced controls.</p>
          <p><strong>Series Management:</strong> Toggle visibility, change colors, and switch chart types.</p>
          <p><strong>Export:</strong> Use the export button to download data as JSON.</p>
        </div>
      </div>
    </div>
  );
};

export default GraphExamples;
