import React from "react";
import { AnimatePresence, motion } from "framer-motion";
import {
    AngleDiagramParams,
    AnimationType,
    DiagramDefinition,
    PolygonDiagramParams,
    TransitionType,
    VennDiagramParams,
    GraphDiagramParams,
} from "../diagramTypes";
import VennDiagram from "./diagrams/VennDiagram";
import PolygonDiagram from "./diagrams/PolygonDiagram";
import AngleDiagram from "./diagrams/AngleDiagram";
import GraphComponent from "./diagrams/GraphComponent";
import transitions from "./transitions";
import animations from "./animations";

interface CompositeDiagramWithTransitionsProps {
    definition: DiagramDefinition;
    transitionType?: TransitionType,
    animationType?: AnimationType
}


const CompositeDiagramWithTransitions: React.FC<CompositeDiagramWithTransitionsProps> = ({
    definition,
    transitionType = "zoom",
    animationType
}) => {
    const transitionStyle = transitions[transitionType] ?? transitions.zoom;
    const animationStyle = animationType ? animations[animationType].animate : {};

    console.log("animationStyle:", animationStyle)

    return (
        <AnimatePresence mode="wait">
            <motion.div
                key={definition.diagramType + JSON.stringify(definition)}
                initial={transitionStyle.initial}
                animate={transitionStyle.animate}
                exit={transitionStyle.exit}
                transition={transitionStyle.transition}
                style={{ perspective: 1000 }} // helpful for 3D effects like flip
            >
                <motion.div
                    key={definition.diagramType + JSON.stringify(definition) + "2"}
                    animate={animationStyle}
                    style={{ perspective: 1000 }} // helpful for 3D effects like flip
                >
                    {definition.diagramType === "venn" && (
                        <VennDiagram params={definition as VennDiagramParams} />
                    )}
                    {definition.diagramType === "polygon" && (
                        <PolygonDiagram params={definition as PolygonDiagramParams} />
                    )}
                    {definition.diagramType === "angle" && (
                        <AngleDiagram params={definition as AngleDiagramParams} />
                    )}
                    {definition.diagramType === "graph" && (
                        <GraphComponent params={definition as GraphDiagramParams} />
                    )}
                </motion.div>
            </motion.div>
        </AnimatePresence>
    );
};

export default CompositeDiagramWithTransitions;
