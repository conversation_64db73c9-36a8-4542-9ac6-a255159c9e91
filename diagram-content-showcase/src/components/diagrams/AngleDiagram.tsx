import React, { useEffect, useRef, useState } from "react";
import { AngleDiagramParams } from "../../diagramTypes";

interface AngleDiagramProps {
  params: AngleDiagramParams;
  size?: number;
  radius?: number;
}

const AngleDiagram: React.FC<AngleDiagramProps> = ({
  params,
  size = 400,
  radius = 40,
}) => {
  const {
    draggable = "none",
    onAngleChange,
    snapIncrement = 15,
    drawClockwise = false,
    arcColor = "orange",
    lineColor = "black",
    labelColor = "black",
    fontSize = 14,
    labels,
    angleLabel,
    highlightColor = lineColor,
    showHandles = true,
  } = params;

  // Internal state fallback
  const [internalFromDeg, setInternalFromDeg] = useState(params.fromAngleDeg ?? 0);
  const [internalToDeg, setInternalToDeg] = useState(params.toAngleDeg ?? 60);
  const [dragged, setDragged] = useState(true);


  const fromAngleDeg = onAngleChange ? (params.fromAngleDeg ?? 0) : internalFromDeg;
  const toAngleDeg = onAngleChange ? (params.toAngleDeg ?? 60) : internalToDeg;

  const svgRef = useRef<SVGSVGElement | null>(null);
  const center = { x: size / 2, y: size / 2 };

  const normalizedFromRad = ((fromAngleDeg % 360 + 360) % 360) * Math.PI / 180;
  const normalizedToRad = ((toAngleDeg % 360 + 360) % 360) * Math.PI / 180;

  const effectiveToRad = drawClockwise ? -normalizedToRad : normalizedToRad;
  const effectiveFromRad = drawClockwise ? -normalizedFromRad : normalizedFromRad;

  const angleDeg = ((toAngleDeg - fromAngleDeg + 360) % 360);
  const angleRad = (angleDeg * Math.PI) / 180;

  const rayLength = size / 3;
  const labelOffset = 10;
  const vertexOffset = 15;
  const arcLabelOffset = 15;
  const handleOffset = 12;

  const [dragTarget, setDragTarget] = useState<"from" | "to" | null>(null);
  const [hoverTarget, setHoverTarget] = useState<"from" | "to" | null>(null);

  const from = {
    x: center.x + Math.cos(effectiveFromRad) * rayLength,
    y: center.y - Math.sin(effectiveFromRad) * rayLength,
  };
  const to = {
    x: center.x + Math.cos(effectiveToRad) * rayLength,
    y: center.y - Math.sin(effectiveToRad) * rayLength,
  };

  const fromHandle = {
    x: from.x + Math.cos(effectiveFromRad) * handleOffset,
    y: from.y - Math.sin(effectiveFromRad) * handleOffset,
  };
  const toHandle = {
    x: to.x + Math.cos(effectiveToRad) * handleOffset,
    y: to.y - Math.sin(effectiveToRad) * handleOffset,
  };

  const arcStart = {
    x: center.x + Math.cos(effectiveFromRad) * radius,
    y: center.y - Math.sin(effectiveFromRad) * radius,
  };
  const arcEnd = {
    x: center.x + Math.cos(effectiveToRad) * radius,
    y: center.y - Math.sin(effectiveToRad) * radius,
  };

  const sweepFlag = drawClockwise ? 1 : 0;
  const largeArcFlag = angleDeg > 180 ? 1 : 0;

  const arcPath = `
    M ${arcStart.x} ${arcStart.y}
    A ${radius} ${radius} 0 ${largeArcFlag} ${sweepFlag} ${arcEnd.x} ${arcEnd.y}
  `;

  const arcDirection = drawClockwise ? -1 : 1;
  const arcMidDeg = ((fromAngleDeg % 360 + 360) % 360) + angleDeg / 2;
  const labelAngleRad = (drawClockwise ? -arcMidDeg : arcMidDeg) * Math.PI / 180;

  const dynamicBoost = angleDeg < 45 ? 10 : angleDeg > 180 ? 20 : 0;
  const labelRadius = radius + arcLabelOffset + dynamicBoost;

  const angleLabelPos = {
    x: center.x + Math.cos(labelAngleRad) * labelRadius,
    y: center.y - Math.sin(labelAngleRad) * labelRadius,
  };

  const vertexLabel = {
    x: center.x - Math.cos(labelAngleRad) * vertexOffset,
    y: center.y + Math.sin(labelAngleRad) * vertexOffset,
  };

  const extendedLabelOffset = labelOffset + (showHandles ? handleOffset + 10 : 0);
  const fromLabel = {
    x: from.x + Math.cos(effectiveFromRad) * extendedLabelOffset,
    y: from.y - Math.sin(effectiveFromRad) * extendedLabelOffset,
  };
  const toLabel = {
    x: to.x + Math.cos(effectiveToRad) * extendedLabelOffset,
    y: to.y - Math.sin(effectiveToRad) * extendedLabelOffset,
  };

  // Drag logic
  useEffect(() => {
    if (!dragTarget) return;

    const handleMove = (e: MouseEvent) => {
      if (!svgRef.current) return;
      const rect = svgRef.current.getBoundingClientRect();
      const mouseX = e.clientX - rect.left;
      const mouseY = e.clientY - rect.top;
      const dx = mouseX - center.x;
      const dy = center.y - mouseY;

      let rawAngle = Math.atan2(dy, dx) * (180 / Math.PI);
      if (drawClockwise) rawAngle = -rawAngle;

      const snapped = Math.round(rawAngle / snapIncrement) * snapIncrement;

      if (onAngleChange) {
        onAngleChange(
          dragTarget === "to" ? { toAngleDeg: snapped } : { fromAngleDeg: snapped }
        );

        if (!dragged) setDragged(true);
      } else {
        if (dragTarget === "to") setInternalToDeg(snapped);
        else setInternalFromDeg(snapped);
        if (!dragged) setDragged(true);
      }
    };

    const handleUp = () => setDragTarget(null);

    window.addEventListener("mousemove", handleMove);
    window.addEventListener("mouseup", handleUp);
    return () => {
      window.removeEventListener("mousemove", handleMove);
      window.removeEventListener("mouseup", handleUp);
    };
  }, [dragTarget, center, drawClockwise, snapIncrement, onAngleChange]);

  useEffect(() => {
    if (!onAngleChange && !dragTarget) {
      setInternalFromDeg(params.fromAngleDeg ?? 0);
      setInternalToDeg(params.toAngleDeg ?? 60);
    }
  }, [params.fromAngleDeg, params.toAngleDeg, onAngleChange, dragTarget]);


  return (
    <svg ref={svgRef} width={size} height={size} style={{ userSelect: "none" }}>
      {/* FROM ray */}
      <line
        x1={center.x}
        y1={center.y}
        x2={from.x}
        y2={from.y}
        stroke={hoverTarget === "from" ? highlightColor : lineColor}
        strokeWidth={hoverTarget === "from" || dragTarget === "from" ? 3 : 1}
      />
      {showHandles && (draggable === "from" || draggable === "both") && (
        <>
          <line
            x1={center.x}
            y1={center.y}
            x2={from.x}
            y2={from.y}
            stroke="transparent"
            strokeWidth={20}
            pointerEvents="stroke"
            onMouseDown={() => setDragTarget("from")}
            onMouseEnter={() => setHoverTarget("from")}
            onMouseLeave={() => setHoverTarget(null)}
            style={{ cursor: dragTarget === "from" ? "grabbing" : "grab" }}
          />
          <circle
            cx={fromHandle.x}
            cy={fromHandle.y}
            r={6}
            fill={hoverTarget === "from" ? highlightColor : "#fff"}
            stroke={lineColor}
            strokeWidth={1.5}
            opacity={0.9}
            onMouseDown={() => setDragTarget("from")}
            onMouseEnter={() => setHoverTarget("from")}
            onMouseLeave={() => setHoverTarget(null)}
            style={{
              cursor: dragTarget === "from" ? "grabbing" : "grab",
              filter: "drop-shadow(0 0 2px rgba(0,0,0,0.3))",
            }}
          />
        </>
      )}

      {/* TO ray */}
      <line
        x1={center.x}
        y1={center.y}
        x2={to.x}
        y2={to.y}
        stroke={hoverTarget === "to" ? highlightColor : lineColor}
        strokeWidth={hoverTarget === "to" || dragTarget === "to" ? 3 : 1}
      />
      {showHandles && (draggable === "to" || draggable === "both") && (
        <>
          <line
            x1={center.x}
            y1={center.y}
            x2={to.x}
            y2={to.y}
            stroke="transparent"
            strokeWidth={100}
            pointerEvents="stroke"
            onMouseDown={() => setDragTarget("to")}
            onMouseEnter={() => setHoverTarget("to")}
            onMouseLeave={() => setHoverTarget(null)}
            style={{ cursor: dragTarget === "to" ? "grabbing" : "grab" }}
          />
          <circle
            cx={toHandle.x}
            cy={toHandle.y}
            r={6}
            fill={hoverTarget === "to" ? highlightColor : "#fff"}
            stroke={lineColor}
            strokeWidth={1.5}
            opacity={0.9}
            onMouseDown={() => setDragTarget("to")}
            onMouseEnter={() => setHoverTarget("to")}
            onMouseLeave={() => setHoverTarget(null)}
            style={{
              cursor: dragTarget === "to" ? "grabbing" : "grab",
              filter: "drop-shadow(0 0 2px rgba(0,0,0,0.3))",
            }}
          />
        </>
      )}

      {/* Arc */}
      <path d={arcPath} fill="none" stroke={arcColor} strokeWidth={1} />

      {/* Labels */}
      {labels?._from && (
        <text
          x={fromLabel.x}
          y={fromLabel.y}
          fontSize={fontSize}
          fill={labelColor}
          textAnchor="middle"
          dominantBaseline="middle"
        >
          {labels._from}
        </text>
      )}
      {labels?._to && (
        <text
          x={toLabel.x}
          y={toLabel.y}
          fontSize={fontSize}
          fill={labelColor}
          textAnchor="middle"
          dominantBaseline="middle"
        >
          {labels._to}
        </text>
      )}
      {labels?._vertex && (
        <text
          x={vertexLabel.x}
          y={vertexLabel.y}
          fontSize={fontSize}
          fill={labelColor}
          textAnchor="middle"
          dominantBaseline="middle"
        >
          {labels._vertex}
        </text>
      )}
      <text
        x={angleLabelPos.x}
        y={angleLabelPos.y}
        fontSize={fontSize}
        fill={labelColor}
        textAnchor="middle"
        dominantBaseline="middle"
      >
        {dragged ? `${angleDeg}°` : angleLabel || `${angleDeg}°`}
      </text>
    </svg>
  );
};

export default AngleDiagram;
