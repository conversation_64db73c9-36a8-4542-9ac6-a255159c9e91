import React, { useRef, useEffect, useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { GraphDiagramParams, GraphSeries, DataPoint } from '../../diagramTypes';
import PlotlyGraph from './graphs/PlotlyGraph';
import VisxGraph from './graphs/VisxGraph';
import InteractiveControls from './graphs/InteractiveControls';

interface GraphComponentProps {
  params: GraphDiagramParams;
  onDataChange?: (series: GraphSeries[]) => void;
  onInteractionChange?: (interaction: any) => void;
}

const GraphComponent: React.FC<GraphComponentProps> = ({
  params,
  onDataChange,
  onInteractionChange,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isReady, setIsReady] = useState(false);
  const [currentSeries, setCurrentSeries] = useState<GraphSeries[]>(params.series || []);
  const [interactionState, setInteractionState] = useState({
    zoom: { x: [0, 1], y: [0, 1] },
    selection: null as DataPoint[] | null,
    hoveredPoint: null as DataPoint | null,
    resetZoom: null as number | null,
    toggleGrid: null as number | null,
    gridVisible: params.xAxis?.gridVisible !== false,
  });

  // Determine which graph library to use based on complexity and mode
  const graphLibrary = useMemo(() => {
    const series = params.series || [];
    const hasComplexInteractions = params.mode === 'interactive' && (
      params.interaction?.brush ||
      params.interaction?.select ||
      series.some(s => s.animated)
    );

    const hasMultipleSeries = series.length > 3;
    const hasComplexAnimations = params.animation?.enabled && (params.animation.duration || 0) > 1000;
    
    // Use Plotly for complex interactions and animations
    if (hasComplexInteractions || hasComplexAnimations || hasMultipleSeries) {
      return 'plotly';
    }
    
    // Use Visx for simpler, more customizable graphs
    return 'visx';
  }, [params]);

  useEffect(() => {
    setIsReady(true);
  }, []);

  useEffect(() => {
    setCurrentSeries(params.series || []);
  }, [params.series]);

  const handleSeriesUpdate = (updatedSeries: GraphSeries[]) => {
    setCurrentSeries(updatedSeries);
    onDataChange?.(updatedSeries);
  };

  const handleInteractionUpdate = (update: any) => {
    setInteractionState(prev => {
      const newState = { ...prev, ...update };

      // Clear reset flags after a short delay to prevent continuous resets
      if (update.resetZoom) {
        setTimeout(() => {
          setInteractionState(current => ({ ...current, resetZoom: null }));
        }, 100);
      }

      if (update.toggleGrid) {
        setTimeout(() => {
          setInteractionState(current => ({ ...current, toggleGrid: null }));
        }, 100);
      }

      return newState;
    });
    onInteractionChange?.(update);
  };

  const containerStyle = {
    width: params.width || '100%',
    height: params.height || 400,
    backgroundColor: params.backgroundColor || 'transparent',
    position: 'relative' as const,
    overflow: 'hidden',
  };

  const margin = {
    top: 20,
    right: 20,
    bottom: 40,
    left: 60,
    ...params.margin,
  };

  return (
    <AnimatePresence mode="wait">
      <motion.div
        key={`graph-${params.mode}-${graphLibrary}`}
        ref={containerRef}
        style={containerStyle}
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        transition={{ 
          duration: params.animation?.duration ? params.animation.duration / 1000 : 0.3,
          ease: params.animation?.easing || 'easeOut'
        }}
      >
        {isReady && (
          <>
            {graphLibrary === 'plotly' ? (
              <PlotlyGraph
                params={params}
                series={currentSeries}
                margin={margin}
                interactionState={interactionState}
                onSeriesUpdate={handleSeriesUpdate}
                onInteractionUpdate={handleInteractionUpdate}
              />
            ) : (
              <VisxGraph
                params={params}
                series={currentSeries}
                margin={margin}
                interactionState={interactionState}
                onSeriesUpdate={handleSeriesUpdate}
                onInteractionUpdate={handleInteractionUpdate}
              />
            )}
            
            {params.mode === 'interactive' && (
              <InteractiveControls
                params={params}
                series={currentSeries}
                interactionState={interactionState}
                onSeriesUpdate={handleSeriesUpdate}
                onInteractionUpdate={handleInteractionUpdate}
              />
            )}
          </>
        )}
      </motion.div>
    </AnimatePresence>
  );
};

export default GraphComponent;
