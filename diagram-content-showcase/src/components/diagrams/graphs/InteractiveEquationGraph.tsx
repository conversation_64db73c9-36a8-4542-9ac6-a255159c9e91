import React, { useState, useMemo, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { create, all } from 'mathjs';
import {
  Calculator,
  TrendingUp,
  ScatterChart,
  Activity,
  Grid,
  Settings
} from 'lucide-react';
import GraphComponent from '../GraphComponent';
import { GraphDiagramParams, GraphSeries, DataPoint } from '../../../diagramTypes';

const math = create(all);

type GraphType = 'function' | 'parametric' | 'polar' | 'numberline';

interface GraphTypeConfig {
  id: GraphType;
  name: string;
  icon: React.ReactNode;
  description: string;
  defaultEquation: string;
  placeholder: string;
}

const graphTypes: GraphTypeConfig[] = [
  {
    id: 'function',
    name: 'Function Graph',
    icon: <TrendingUp size={20} />,
    description: 'Plot y = f(x) functions',
    defaultEquation: 'sin(x)',
    placeholder: 'Enter function like: sin(x), x^2, log(x), etc.'
  },
  {
    id: 'parametric',
    name: 'Parametric',
    icon: <Activity size={20} />,
    description: 'Plot parametric equations x(t), y(t)',
    defaultEquation: 'cos(t), sin(t)',
    placeholder: 'Enter as: x(t), y(t) like: cos(t), sin(t)'
  },
  {
    id: 'polar',
    name: 'Polar',
    icon: <ScatterChart size={20} />,
    description: 'Plot polar equations r = f(θ)',
    defaultEquation: '1 + cos(theta)',
    placeholder: 'Enter polar function like: 1 + cos(theta), theta'
  },

  {
    id: 'numberline',
    name: 'Number Line',
    icon: <Grid size={20} />,
    description: 'Interactive number line with inequalities and ranges',
    defaultEquation: 'x > 2',
    placeholder: 'Enter inequality like: x > 2, x <= -1, 2 < x < 5'
  }
];

interface InteractiveEquationGraphProps {
  width?: number;
  height?: number;
  initialType?: GraphType;
  initialEquation?: string;
}

const InteractiveEquationGraph: React.FC<InteractiveEquationGraphProps> = ({
  width = 800,
  height = 600,
  initialType = 'function',
  initialEquation = 'sin(x)'
}) => {
  const [selectedType, setSelectedType] = useState<GraphType>(initialType);
  const [equation, setEquation] = useState(initialEquation);
  const [domain, setDomain] = useState({ min: -10, max: 10 });
  const [range, setRange] = useState({ min: -5, max: 5 });
  const [resolution, setResolution] = useState(200);
  const [error, setError] = useState<string | null>(null);
  const [showControls, setShowControls] = useState(true);
  const [showGuidedForm, setShowGuidedForm] = useState(false);
  const [guidedParams, setGuidedParams] = useState({
    functionType: 'polynomial',
    coefficients: { a: 1, b: 0, c: 0 },
    trigParams: { amplitude: 1, frequency: 1, phase: 0, vertical: 0 }
  });

  const currentGraphType = graphTypes.find(type => type.id === selectedType)!;



  // Generate function data
  const generateFunctionData = useCallback((expr: string, domain: { min: number; max: number }, points: number): GraphSeries[] => {
    const compiledFn = math.compile(expr);
    const data: DataPoint[] = [];

    for (let i = 0; i <= points; i++) {
      const x = domain.min + (i / points) * (domain.max - domain.min);
      try {
        const evalContext = { x, t: 0 };
        const y = compiledFn.evaluate(evalContext);
        if (Number.isFinite(y)) {
          data.push({ x, y });
        }
      } catch {
        // Skip invalid points
      }
    }

    return [{
      id: 'function',
      name: `y = ${expr}`,
      data,
      type: 'line',
      color: '#0077ff',
      strokeWidth: 2,
      animated: true,
      animationDuration: 1500
    }];
  }, []);

  // Generate parametric data
  const generateParametricData = useCallback((expr: string, domain: { min: number; max: number }, points: number): GraphSeries[] => {
    const [xExpr, yExpr] = expr.split(',').map(e => e.trim());
    if (!xExpr || !yExpr) throw new Error('Parametric equations need both x(t) and y(t)');

    const xFn = math.compile(xExpr);
    const yFn = math.compile(yExpr);
    const data: DataPoint[] = [];

    for (let i = 0; i <= points; i++) {
      const t = domain.min + (i / points) * (domain.max - domain.min);
      try {
        const evalContext = { t };
        const x = xFn.evaluate(evalContext);
        const y = yFn.evaluate(evalContext);
        if (Number.isFinite(x) && Number.isFinite(y)) {
          data.push({ x, y });
        }
      } catch {
        // Skip invalid points
      }
    }

    return [{
      id: 'parametric',
      name: `x = ${xExpr}, y = ${yExpr}`,
      data,
      type: 'line',
      color: '#ff3366',
      strokeWidth: 2,
      animated: true,
      animationDuration: 1500
    }];
  }, []);

  // Generate polar data
  const generatePolarData = useCallback((expr: string, domain: { min: number; max: number }, points: number): GraphSeries[] => {
    const compiledFn = math.compile(expr);
    const data: DataPoint[] = [];
    
    for (let i = 0; i <= points; i++) {
      const theta = domain.min + (i / points) * (domain.max - domain.min);
      try {
        const r = compiledFn.evaluate({ theta });
        if (Number.isFinite(r)) {
          const x = r * Math.cos(theta);
          const y = r * Math.sin(theta);
          data.push({ x, y });
        }
      } catch {
        // Skip invalid points
      }
    }

    return [{
      id: 'polar',
      name: `r = ${expr}`,
      data,
      type: 'line',
      color: '#00cc88',
      strokeWidth: 2,
      animated: true,
      animationDuration: 1500
    }];
  }, []);



  // Generate number line data with inequality support
  const generateNumberLineData = useCallback((expr: string): GraphSeries[] => {
    try {
      // Handle different inequality formats
      const series: GraphSeries[] = [];

      // Parse inequalities like "x > 2", "x <= -1", "2 < x < 5"
      if (expr.includes('<') || expr.includes('>') || expr.includes('≤') || expr.includes('≥')) {
        const inequalities = expr.split(',').map(s => s.trim());

        inequalities.forEach((ineq, index) => {
          const points = parseInequality(ineq);
          if (points.length > 0) {
            series.push({
              id: `inequality-${index}`,
              name: ineq,
              data: points,
              type: 'scatter',
              color: ['#ff9900', '#00cc88', '#ff3366', '#9966cc'][index % 4],
              animated: true,
              animationDuration: 1000
            });
          }
        });
      } else {
        // Handle simple number lists
        const numbers = expr.split(',').map(n => parseFloat(n.trim())).filter(n => !isNaN(n));
        const data: DataPoint[] = numbers.map(n => ({ x: n, y: 0, size: 8 }));

        series.push({
          id: 'numberline',
          name: 'Number Line Points',
          data,
          type: 'scatter',
          color: '#ff9900',
          animated: true,
          animationDuration: 1000
        });
      }

      return series;
    } catch (error) {
      // Fallback to simple points
      const data: DataPoint[] = [{ x: 0, y: 0, size: 8 }];
      return [{
        id: 'numberline',
        name: 'Number Line',
        data,
        type: 'scatter',
        color: '#ff9900',
        animated: true,
        animationDuration: 1000
      }];
    }
  }, []);

  // Parse inequality expressions
  const parseInequality = useCallback((ineq: string): DataPoint[] => {
    const points: DataPoint[] = [];

    // Handle compound inequalities like "2 < x < 5"
    if (ineq.includes('<') && ineq.split('<').length === 3) {
      const parts = ineq.split('<').map(s => s.trim());
      const min = parseFloat(parts[0]);
      const max = parseFloat(parts[2]);

      if (!isNaN(min) && !isNaN(max)) {
        // Create range visualization with more visible dots
        for (let x = min; x <= max; x += 0.05) {
          points.push({ x, y: 0, size: 6, color: '#ff6600' });
        }
        // Add endpoints with larger, more visible circles
        points.push({ x: min, y: 0, size: 14, color: '#ff0000' }); // Open circle
        points.push({ x: max, y: 0, size: 14, color: '#ff0000' }); // Open circle
      }
    } else {
      // Handle simple inequalities
      const match = ineq.match(/(x|X)\s*([><=≥≤]+)\s*(-?\d+\.?\d*)/);
      if (match) {
        const operator = match[2];
        const value = parseFloat(match[3]);

        if (!isNaN(value)) {
          // Create visualization based on operator
          const start = value - 5;
          const end = value + 5;

          for (let x = start; x <= end; x += 0.1) {
            let include = false;

            switch (operator) {
              case '>':
                include = x > value;
                break;
              case '>=':
              case '≥':
                include = x >= value;
                break;
              case '<':
                include = x < value;
                break;
              case '<=':
              case '≤':
                include = x <= value;
                break;
            }

            if (include) {
              points.push({ x, y: 0, size: 6, color: '#ff6600' }); // Larger, more visible dots
            }
          }

          // Add the boundary point with larger, more visible size
          const isInclusive = operator.includes('=') || operator.includes('≥') || operator.includes('≤');
          points.push({
            x: value,
            y: 0,
            size: 16,
            color: isInclusive ? '#00cc88' : '#ff0000' // Filled vs open circle
          });
        }
      }
    }

    return points;
  }, []);

  // Generate data based on graph type and equation
  const graphData = useMemo(() => {
    try {
      setError(null);

      switch (selectedType) {
        case 'function':
          return generateFunctionData(equation, domain, resolution);
        case 'parametric':
          return generateParametricData(equation, domain, resolution);
        case 'polar':
          return generatePolarData(equation, domain, resolution);
        case 'numberline':
          return generateNumberLineData(equation);
        default:
          return generateFunctionData('sin(x)', domain, resolution);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Invalid equation');
      return generateFunctionData('0', domain, resolution);
    }
  }, [selectedType, equation, domain, resolution, generateFunctionData, generateParametricData, generatePolarData, generateNumberLineData]);

  // Create graph parameters
  const graphParams: GraphDiagramParams = {
    diagramType: 'graph',
    mode: 'interactive',
    width,
    height: height - 120, // Account for controls
    series: graphData,
    xAxis: {
      label: selectedType === 'numberline' ? 'Number Line' : 'X',
      gridVisible: true,
      gridColor: '#e0e0e0',
      min: domain.min,
      max: domain.max
    },
    yAxis: {
      label: selectedType === 'numberline' ? '' : 'Y',
      gridVisible: selectedType !== 'numberline',
      gridColor: '#e0e0e0',
      min: selectedType === 'numberline' ? -1 : range.min,
      max: selectedType === 'numberline' ? 1 : range.max
    },
    legend: {
      visible: true,
      position: selectedType === 'numberline' ? 'bottom' : 'bottom-right'
    },
    tooltip: {
      enabled: true,
      format: selectedType === 'numberline' ? '%{x}' : '(%{x:.2f}, %{y:.2f})'
    },
    interaction: {
      zoom: true,
      pan: true,
      hover: true,
      crosshair: selectedType !== 'numberline'
    },
    animation: {
      enabled: true,
      duration: 1500,
      easing: 'easeOut'
    },
    backgroundColor: '#fafafa'
  };

  const handleTypeChange = (type: GraphType) => {
    setSelectedType(type);
    const config = graphTypes.find(t => t.id === type)!;
    setEquation(config.defaultEquation);
    
    // Adjust domain/range for different types
    if (type === 'polar') {
      setDomain({ min: 0, max: 2 * Math.PI });
    } else if (type === 'parametric') {
      setDomain({ min: 0, max: 2 * Math.PI });
    } else if (type === 'numberline') {
      setDomain({ min: -10, max: 10 });
      setRange({ min: -1, max: 1 });
    } else {
      setDomain({ min: -10, max: 10 });
      setRange({ min: -5, max: 5 });
    }
  };

  // Generate equation from guided form
  const generateGuidedEquation = () => {
    const { functionType, coefficients, trigParams } = guidedParams;

    switch (functionType) {
      case 'polynomial':
        const { a, b, c } = coefficients;
        if (c !== 0) return `${a}*x^2 + ${b}*x + ${c}`;
        if (b !== 0) return `${a}*x + ${b}`;
        return `${a}`;

      case 'trigonometric':
        const { amplitude, frequency, phase, vertical } = trigParams;
        return `${amplitude}*sin(${frequency}*x + ${phase}) + ${vertical}`;

      case 'exponential':
        return `${coefficients.a}*exp(${coefficients.b}*x) + ${coefficients.c}`;

      case 'logarithmic':
        return `${coefficients.a}*log(${coefficients.b}*x + ${coefficients.c})`;

      default:
        return 'x';
    }
  };

  const applyGuidedEquation = () => {
    const newEquation = generateGuidedEquation();
    setEquation(newEquation);
    setShowGuidedForm(false);
  };

  return (
    <div className="w-full bg-white rounded-lg shadow-lg overflow-hidden">
      {/* Header with Graph Type Selector */}
      <div className="bg-blue-600 text-white p-4">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold flex items-center gap-2">
            <Calculator size={24} />
            Interactive Equation Graph
          </h2>
          <div className="flex gap-2">
            {/* Only show guided builder for function, parametric, and polar graphs */}
            {(selectedType === 'function' || selectedType === 'parametric' || selectedType === 'polar') && (
              <button
                onClick={() => setShowGuidedForm(!showGuidedForm)}
                className={`p-2 rounded-lg transition-colors ${
                  showGuidedForm
                    ? 'bg-white text-blue-600'
                    : 'bg-white/20 hover:bg-white/30 text-white'
                }`}
                title="Guided Function Builder"
              >
                <Calculator size={20} />
              </button>
            )}
            <button
              onClick={() => setShowControls(!showControls)}
              className={`p-2 rounded-lg transition-colors ${
                showControls
                  ? 'bg-white text-blue-600'
                  : 'bg-white/20 hover:bg-white/30 text-white'
              }`}
              title="Advanced Controls"
            >
              <Settings size={20} />
            </button>
          </div>
        </div>

        {/* Graph Type Tabs */}
        <div className="flex flex-wrap gap-2">
          {graphTypes.map((type) => (
            <button
              key={type.id}
              onClick={() => handleTypeChange(type.id)}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                selectedType === type.id
                  ? 'bg-white text-blue-600 font-medium'
                  : 'bg-white/20 hover:bg-white/30 text-white'
              }`}
            >
              {type.icon}
              <span className="hidden sm:inline">{type.name}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Guided Form Panel - Only for function, parametric, and polar */}
      <AnimatePresence>
        {showGuidedForm && (selectedType === 'function' || selectedType === 'parametric' || selectedType === 'polar') && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            className="border-b border-gray-200 bg-gradient-to-r from-purple-50 to-blue-50 p-4"
          >
            <div className="max-w-4xl mx-auto">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">Guided Function Builder</h3>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {/* Function Type Selection */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Function Type</label>
                  <select
                    value={guidedParams.functionType}
                    onChange={(e) => setGuidedParams(prev => ({ ...prev, functionType: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="polynomial">Polynomial (ax² + bx + c)</option>
                    <option value="trigonometric">Trigonometric (a·sin(bx + c) + d)</option>
                    <option value="exponential">Exponential (a·e^(bx) + c)</option>
                    <option value="logarithmic">Logarithmic (a·ln(bx + c))</option>
                  </select>
                </div>

                {/* Parameters */}
                <div className="md:col-span-2">
                  {guidedParams.functionType === 'polynomial' && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Polynomial Coefficients: y = ax² + bx + c
                      </label>
                      <div className="grid grid-cols-3 gap-3">
                        <div>
                          <label className="block text-xs text-gray-600">a (x² coefficient)</label>
                          <input
                            type="number"
                            value={guidedParams.coefficients.a}
                            onChange={(e) => setGuidedParams(prev => ({
                              ...prev,
                              coefficients: { ...prev.coefficients, a: parseFloat(e.target.value) || 0 }
                            }))}
                            className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                            step="0.1"
                          />
                        </div>
                        <div>
                          <label className="block text-xs text-gray-600">b (x coefficient)</label>
                          <input
                            type="number"
                            value={guidedParams.coefficients.b}
                            onChange={(e) => setGuidedParams(prev => ({
                              ...prev,
                              coefficients: { ...prev.coefficients, b: parseFloat(e.target.value) || 0 }
                            }))}
                            className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                            step="0.1"
                          />
                        </div>
                        <div>
                          <label className="block text-xs text-gray-600">c (constant)</label>
                          <input
                            type="number"
                            value={guidedParams.coefficients.c}
                            onChange={(e) => setGuidedParams(prev => ({
                              ...prev,
                              coefficients: { ...prev.coefficients, c: parseFloat(e.target.value) || 0 }
                            }))}
                            className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                            step="0.1"
                          />
                        </div>
                      </div>
                    </div>
                  )}

                  {guidedParams.functionType === 'trigonometric' && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Trigonometric Parameters: y = a·sin(bx + c) + d
                      </label>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                        <div>
                          <label className="block text-xs text-gray-600">a (amplitude)</label>
                          <input
                            type="number"
                            value={guidedParams.trigParams.amplitude}
                            onChange={(e) => setGuidedParams(prev => ({
                              ...prev,
                              trigParams: { ...prev.trigParams, amplitude: parseFloat(e.target.value) || 1 }
                            }))}
                            className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                            step="0.1"
                          />
                        </div>
                        <div>
                          <label className="block text-xs text-gray-600">b (frequency)</label>
                          <input
                            type="number"
                            value={guidedParams.trigParams.frequency}
                            onChange={(e) => setGuidedParams(prev => ({
                              ...prev,
                              trigParams: { ...prev.trigParams, frequency: parseFloat(e.target.value) || 1 }
                            }))}
                            className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                            step="0.1"
                          />
                        </div>
                        <div>
                          <label className="block text-xs text-gray-600">c (phase shift)</label>
                          <input
                            type="number"
                            value={guidedParams.trigParams.phase}
                            onChange={(e) => setGuidedParams(prev => ({
                              ...prev,
                              trigParams: { ...prev.trigParams, phase: parseFloat(e.target.value) || 0 }
                            }))}
                            className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                            step="0.1"
                          />
                        </div>
                        <div>
                          <label className="block text-xs text-gray-600">d (vertical shift)</label>
                          <input
                            type="number"
                            value={guidedParams.trigParams.vertical}
                            onChange={(e) => setGuidedParams(prev => ({
                              ...prev,
                              trigParams: { ...prev.trigParams, vertical: parseFloat(e.target.value) || 0 }
                            }))}
                            className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                            step="0.1"
                          />
                        </div>
                      </div>
                    </div>
                  )}

                  {(guidedParams.functionType === 'exponential' || guidedParams.functionType === 'logarithmic') && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        {guidedParams.functionType === 'exponential' ? 'Exponential' : 'Logarithmic'} Parameters
                      </label>
                      <div className="grid grid-cols-3 gap-3">
                        <div>
                          <label className="block text-xs text-gray-600">a (scale)</label>
                          <input
                            type="number"
                            value={guidedParams.coefficients.a}
                            onChange={(e) => setGuidedParams(prev => ({
                              ...prev,
                              coefficients: { ...prev.coefficients, a: parseFloat(e.target.value) || 1 }
                            }))}
                            className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                            step="0.1"
                          />
                        </div>
                        <div>
                          <label className="block text-xs text-gray-600">b (rate/base)</label>
                          <input
                            type="number"
                            value={guidedParams.coefficients.b}
                            onChange={(e) => setGuidedParams(prev => ({
                              ...prev,
                              coefficients: { ...prev.coefficients, b: parseFloat(e.target.value) || 1 }
                            }))}
                            className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                            step="0.1"
                          />
                        </div>
                        <div>
                          <label className="block text-xs text-gray-600">c (shift)</label>
                          <input
                            type="number"
                            value={guidedParams.coefficients.c}
                            onChange={(e) => setGuidedParams(prev => ({
                              ...prev,
                              coefficients: { ...prev.coefficients, c: parseFloat(e.target.value) || 0 }
                            }))}
                            className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                            step="0.1"
                          />
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Preview and Apply */}
              <div className="mt-4 flex items-center justify-between">
                <div className="text-sm text-gray-600">
                  <strong>Preview:</strong> {generateGuidedEquation()}
                </div>
                <button
                  onClick={applyGuidedEquation}
                  className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
                >
                  Apply Function
                </button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Controls Panel */}
      <AnimatePresence>
        {showControls && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            className="border-b border-gray-200 bg-gray-50 p-4"
          >
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Equation Input */}
              <div className="lg:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {currentGraphType.name} Equation
                </label>
                <input
                  type="text"
                  value={equation}
                  onChange={(e) => setEquation(e.target.value)}
                  placeholder={currentGraphType.placeholder}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
                <p className="text-xs text-gray-500 mt-1">{currentGraphType.description}</p>
                {error && <p className="text-xs text-red-500 mt-1">{error}</p>}
              </div>

              {/* Domain Controls */}
              {(
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {selectedType === 'parametric' ? 't Range' : selectedType === 'polar' ? 'θ Range' : 'X Domain'}
                  </label>
                  <div className="flex gap-2">
                    <input
                      type="number"
                      value={domain.min}
                      onChange={(e) => setDomain(prev => ({ ...prev, min: parseFloat(e.target.value) }))}
                      className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                      step="0.1"
                    />
                    <input
                      type="number"
                      value={domain.max}
                      onChange={(e) => setDomain(prev => ({ ...prev, max: parseFloat(e.target.value) }))}
                      className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                      step="0.1"
                    />
                  </div>
                </div>
              )}

              {/* Range Controls */}
              {selectedType !== 'numberline' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Y Range</label>
                  <div className="flex gap-2">
                    <input
                      type="number"
                      value={range.min}
                      onChange={(e) => setRange(prev => ({ ...prev, min: parseFloat(e.target.value) }))}
                      className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                      step="0.1"
                    />
                    <input
                      type="number"
                      value={range.max}
                      onChange={(e) => setRange(prev => ({ ...prev, max: parseFloat(e.target.value) }))}
                      className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                      step="0.1"
                    />
                  </div>
                </div>
              )}
            </div>

            {/* Additional Controls */}
            <div className="flex flex-wrap items-center gap-4 mt-4 pt-4 border-t border-gray-200">
              <div className="flex items-center gap-2">
                <label className="text-sm text-gray-700">Resolution:</label>
                <input
                  type="range"
                  min="50"
                  max="500"
                  step="10"
                  value={resolution}
                  onChange={(e) => setResolution(parseInt(e.target.value))}
                  className="w-20"
                />
                <span className="text-sm text-gray-600">{resolution}</span>
              </div>


            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Graph Display */}
      <div className="relative">
        <GraphComponent params={graphParams} />
      </div>

      {/* Quick Examples */}
      <div className="bg-gray-50 p-4 border-t border-gray-200">
        <h4 className="text-sm font-medium text-gray-700 mb-2">Quick Examples:</h4>
        <div className="flex flex-wrap gap-2">
          {getExamplesForType(selectedType).map((example, index) => (
            <button
              key={index}
              onClick={() => setEquation(example)}
              className="px-3 py-1 bg-white border border-gray-300 rounded text-sm hover:bg-gray-50 transition-colors"
            >
              {example}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};

// Helper function to get examples for each graph type
const getExamplesForType = (type: GraphType): string[] => {
  switch (type) {
    case 'function':
      return ['sin(x)', 'cos(x)', 'x^2', 'sin(x + t)', 'cos(x + t)', 'sin(x) * cos(t)', 'x^2 + sin(t)', 'log(x)', 'exp(x)'];
    case 'parametric':
      return ['cos(t), sin(t)', 't, t^2', 'cos(3*t), sin(2*t)', 't*cos(t), t*sin(t)'];
    case 'polar':
      return ['1 + cos(theta)', 'sin(2*theta)', 'theta', '2 + sin(4*theta)', 'cos(3*theta)'];

    case 'numberline':
      return ['x > 2', 'x <= -1', '2 < x < 5', 'x >= 0', '-3, -1, 0, 2, 4', 'x > -2, x < 3'];
    default:
      return [];
  }
};

export default InteractiveEquationGraph;
