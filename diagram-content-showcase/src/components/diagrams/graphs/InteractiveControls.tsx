import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  <PERSON>rid, 
  Eye, 
  EyeOff, 
  ZoomIn, 
  ZoomOut, 
  RotateCcw, 
  <PERSON>ting<PERSON>,
  <PERSON><PERSON>,
  Bar<PERSON>hart3,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Download,
  Play,
  Pause
} from 'lucide-react';
import { GraphDiagramParams, GraphSeries } from '../../../diagramTypes';

interface InteractiveControlsProps {
  params: GraphDiagramParams;
  series: GraphSeries[];
  interactionState: any;
  onSeriesUpdate: (series: GraphSeries[]) => void;
  onInteractionUpdate: (update: any) => void;
}

const InteractiveControls: React.FC<InteractiveControlsProps> = ({
  params,
  series,
  interactionState,
  onSeriesUpdate,
  onInteractionUpdate,
}) => {
  const [showControls, setShowControls] = useState(false);
  const [activePanel, setActivePanel] = useState<'series' | 'style' | 'animation' | null>(null);
  const [localGridVisible, setLocalGridVisible] = useState(params.xAxis?.gridVisible !== false);
  const [isResetting, setIsResetting] = useState(false);

  const toggleSeriesVisibility = (seriesId: string) => {
    const updatedSeries = series.map(s => 
      s.id === seriesId ? { ...s, visible: s.visible !== false ? false : true } : s
    );
    onSeriesUpdate(updatedSeries);
  };

  const updateSeriesColor = (seriesId: string, color: string) => {
    const updatedSeries = series.map(s => 
      s.id === seriesId ? { ...s, color } : s
    );
    onSeriesUpdate(updatedSeries);
  };

  const updateSeriesType = (seriesId: string, type: GraphSeries['type']) => {
    const updatedSeries = series.map(s => 
      s.id === seriesId ? { ...s, type } : s
    );
    onSeriesUpdate(updatedSeries);
  };

  const resetZoom = () => {
    // Visual feedback for reset action
    setIsResetting(true);
    setTimeout(() => setIsResetting(false), 500);

    // Reset zoom by triggering a zoom reset event
    onInteractionUpdate({
      resetZoom: Date.now(), // Use timestamp to ensure uniqueness
      zoom: { x: null, y: null } // Reset to auto-scale
    });
  };

  const toggleGrid = () => {
    // Toggle grid visibility using local state
    const newGridVisible = !localGridVisible;
    setLocalGridVisible(newGridVisible);
    onInteractionUpdate({
      toggleGrid: Date.now(), // Use timestamp to ensure uniqueness
      gridVisible: newGridVisible
    });
  };

  const exportData = () => {
    const dataStr = JSON.stringify(series, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'graph-data.json';
    link.click();
    URL.revokeObjectURL(url);
  };

  const controlsVariants = {
    hidden: { opacity: 0, x: 20 },
    visible: { opacity: 1, x: 0 },
  };

  const panelVariants = {
    hidden: { opacity: 0, height: 0 },
    visible: { opacity: 1, height: 'auto' },
  };

  return (
    <div className="absolute top-4 left-4 z-20">
      {/* Main Controls Toggle */}
      <motion.button
        className="bg-white/90 backdrop-blur-sm border border-gray-200 rounded-lg p-2 shadow-lg hover:bg-white transition-colors"
        onClick={() => setShowControls(!showControls)}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        title="Graph Controls"
      >
        <Settings size={20} className="text-gray-700" />
      </motion.button>

      {/* Controls Panel */}
      <AnimatePresence>
        {showControls && (
          <motion.div
            className="mt-2 bg-white/95 backdrop-blur-sm border border-gray-200 rounded-lg shadow-xl p-4 min-w-[320px] max-w-[400px]"
            variants={controlsVariants}
            initial="hidden"
            animate="visible"
            exit="hidden"
            transition={{ duration: 0.2 }}
          >
            {/* Quick Actions */}
            <div className="flex flex-wrap gap-2 mb-4">
              <button
                className={`flex items-center gap-1 px-3 py-1 rounded text-sm transition-colors ${
                  isResetting
                    ? 'bg-blue-200 text-blue-800'
                    : 'bg-blue-100 hover:bg-blue-200 text-blue-700'
                }`}
                onClick={resetZoom}
                disabled={isResetting}
              >
                <RotateCcw size={14} className={isResetting ? 'animate-spin' : ''} />
                {isResetting ? 'Resetting...' : 'Reset'}
              </button>
              <button
                className={`flex items-center gap-1 px-3 py-1 rounded text-sm transition-colors ${
                  localGridVisible
                    ? 'bg-green-100 hover:bg-green-200 text-green-700'
                    : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
                }`}
                onClick={toggleGrid}
              >
                <Grid size={14} />
                Grid {localGridVisible ? 'On' : 'Off'}
              </button>
              <button
                className="flex items-center gap-1 px-3 py-1 bg-purple-100 hover:bg-purple-200 rounded text-sm text-purple-700 transition-colors"
                onClick={exportData}
              >
                <Download size={14} />
                Export
              </button>
            </div>

            {/* Panel Toggles */}
            <div className="flex gap-1 mb-3">
              {['series', 'style', 'animation'].map((panel) => (
                <button
                  key={panel}
                  className={`px-3 py-1 rounded text-sm transition-colors ${
                    activePanel === panel
                      ? 'bg-blue-500 text-white'
                      : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
                  }`}
                  onClick={() => setActivePanel(activePanel === panel ? null : panel as any)}
                >
                  {panel.charAt(0).toUpperCase() + panel.slice(1)}
                </button>
              ))}
            </div>

            {/* Series Panel */}
            <AnimatePresence>
              {activePanel === 'series' && (
                <motion.div
                  variants={panelVariants}
                  initial="hidden"
                  animate="visible"
                  exit="hidden"
                  className="border-t pt-3"
                >
                  <h4 className="text-sm font-medium text-gray-700 mb-2">Series Control</h4>
                  <div className="space-y-2 max-h-40 overflow-y-auto">
                    {series.map((seriesItem) => (
                      <div key={seriesItem.id} className="flex items-center gap-2 p-2 bg-gray-50 rounded">
                        <button
                          onClick={() => toggleSeriesVisibility(seriesItem.id)}
                          className="text-gray-600 hover:text-gray-800"
                        >
                          {seriesItem.visible !== false ? <Eye size={16} /> : <EyeOff size={16} />}
                        </button>
                        
                        <div className="flex-1 min-w-0">
                          <div className="text-sm font-medium text-gray-700 truncate">
                            {seriesItem.name}
                          </div>
                          <div className="flex gap-1 mt-1">
                            {['line', 'scatter', 'bar', 'area'].map((type) => (
                              <button
                                key={type}
                                className={`p-1 rounded ${
                                  seriesItem.type === type
                                    ? 'bg-blue-500 text-white'
                                    : 'bg-white hover:bg-gray-100 text-gray-600'
                                }`}
                                onClick={() => updateSeriesType(seriesItem.id, type as GraphSeries['type'])}
                              >
                                {type === 'line' && <LineChart size={12} />}
                                {type === 'scatter' && <ScatterChart size={12} />}
                                {type === 'bar' && <BarChart3 size={12} />}
                                {type === 'area' && <AreaChart size={12} />}
                              </button>
                            ))}
                          </div>
                        </div>

                        <input
                          type="color"
                          value={seriesItem.color || '#0077ff'}
                          onChange={(e) => updateSeriesColor(seriesItem.id, e.target.value)}
                          className="w-6 h-6 rounded border border-gray-300 cursor-pointer"
                        />
                      </div>
                    ))}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Style Panel */}
            <AnimatePresence>
              {activePanel === 'style' && (
                <motion.div
                  variants={panelVariants}
                  initial="hidden"
                  animate="visible"
                  exit="hidden"
                  className="border-t pt-3"
                >
                  <h4 className="text-sm font-medium text-gray-700 mb-2">Style Options</h4>
                  <div className="space-y-3">
                    <div>
                      <label className="block text-xs text-gray-600 mb-1">Theme</label>
                      <select className="w-full px-2 py-1 border border-gray-300 rounded text-sm">
                        <option value="light">Light</option>
                        <option value="dark">Dark</option>
                        <option value="custom">Custom</option>
                      </select>
                    </div>
                    
                    <div>
                      <label className="block text-xs text-gray-600 mb-1">Background</label>
                      <input
                        type="color"
                        className="w-full h-8 rounded border border-gray-300 cursor-pointer"
                        defaultValue={params.backgroundColor || '#ffffff'}
                      />
                    </div>

                    <div className="flex items-center gap-2">
                      <input type="checkbox" id="showLegend" defaultChecked={params.legend?.visible !== false} />
                      <label htmlFor="showLegend" className="text-sm text-gray-700">Show Legend</label>
                    </div>

                    <div className="flex items-center gap-2">
                      <input type="checkbox" id="showTooltip" defaultChecked={params.tooltip?.enabled !== false} />
                      <label htmlFor="showTooltip" className="text-sm text-gray-700">Show Tooltip</label>
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Animation Panel */}
            <AnimatePresence>
              {activePanel === 'animation' && (
                <motion.div
                  variants={panelVariants}
                  initial="hidden"
                  animate="visible"
                  exit="hidden"
                  className="border-t pt-3"
                >
                  <h4 className="text-sm font-medium text-gray-700 mb-2">Animation Control</h4>
                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      <input 
                        type="checkbox" 
                        id="enableAnimation" 
                        defaultChecked={params.animation?.enabled !== false} 
                      />
                      <label htmlFor="enableAnimation" className="text-sm text-gray-700">Enable Animations</label>
                    </div>

                    <div>
                      <label className="block text-xs text-gray-600 mb-1">Duration (ms)</label>
                      <input
                        type="range"
                        min="100"
                        max="3000"
                        step="100"
                        defaultValue={params.animation?.duration || 1000}
                        className="w-full"
                      />
                    </div>

                    <div>
                      <label className="block text-xs text-gray-600 mb-1">Easing</label>
                      <select className="w-full px-2 py-1 border border-gray-300 rounded text-sm">
                        <option value="linear">Linear</option>
                        <option value="easeIn">Ease In</option>
                        <option value="easeOut">Ease Out</option>
                        <option value="easeInOut">Ease In Out</option>
                        <option value="circIn">Circ In</option>
                        <option value="circOut">Circ Out</option>
                        <option value="backIn">Back In</option>
                        <option value="backOut">Back Out</option>
                      </select>
                    </div>

                    <div className="flex gap-2">
                      <button className="flex-1 flex items-center justify-center gap-1 px-3 py-2 bg-green-100 hover:bg-green-200 rounded text-sm text-green-700 transition-colors">
                        <Play size={14} />
                        Play
                      </button>
                      <button className="flex-1 flex items-center justify-center gap-1 px-3 py-2 bg-red-100 hover:bg-red-200 rounded text-sm text-red-700 transition-colors">
                        <Pause size={14} />
                        Pause
                      </button>
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default InteractiveControls;
