import React, { useMemo, useState, useCallback, useEffect } from 'react';
import { Group } from '@visx/group';
import { scaleLinear, scaleOrdinal } from '@visx/scale';
import { AxisBottom, AxisLeft } from '@visx/axis';
import { GridRows, GridColumns } from '@visx/grid';
import { LinePath, AreaClosed, Bar } from '@visx/shape';
import { curveMonotoneX, curveStepAfter, curveCardinal } from '@visx/curve';
import { localPoint } from '@visx/event';
import { useTooltip, TooltipWithBounds, defaultStyles } from '@visx/tooltip';
import { Zoom } from '@visx/zoom';
import { RectClipPath } from '@visx/clip-path';
import { motion } from 'framer-motion';
import { GraphDiagramParams, GraphSeries, DataPoint } from '../../../diagramTypes';

interface VisxGraphProps {
  params: GraphDiagramParams;
  series: GraphSeries[];
  margin: { top: number; right: number; bottom: number; left: number };
  interactionState: any;
  onSeriesUpdate: (series: GraphSeries[]) => void;
  onInteractionUpdate: (update: any) => void;
}

const VisxGraph: React.FC<VisxGraphProps> = ({
  params,
  series,
  margin,
  interactionState,
  onSeriesUpdate,
  onInteractionUpdate,
}) => {
  const [selectedPoints, setSelectedPoints] = useState<DataPoint[]>([]);
  const [gridVisible, setGridVisible] = useState(params.xAxis?.gridVisible !== false);
  const [zoomTransform, setZoomTransform] = useState<any>(null);

  const {
    tooltipData,
    tooltipLeft,
    tooltipTop,
    tooltipOpen,
    showTooltip,
    hideTooltip,
  } = useTooltip<DataPoint>();

  // Handle interaction updates
  useEffect(() => {
    if (interactionState.resetZoom && interactionState.resetZoom !== null) {
      setZoomTransform(null);
    }
  }, [interactionState.resetZoom]);

  useEffect(() => {
    if (interactionState.toggleGrid && interactionState.toggleGrid !== null) {
      setGridVisible(interactionState.gridVisible);
    }
  }, [interactionState.toggleGrid, interactionState.gridVisible]);

  const width = (params.width as number) || 800;
  const height = (params.height as number) || 400;
  const innerWidth = width - margin.left - margin.right;
  const innerHeight = height - margin.top - margin.bottom;

  // Get all data points for scale calculation
  const allData = useMemo(() => {
    return series.filter(s => s.visible !== false).flatMap(s => s.data);
  }, [series]);

  // Create scales
  const xScale = useMemo(() => {
    const domain = params.xAxis?.min !== undefined && params.xAxis?.max !== undefined
      ? [params.xAxis.min, params.xAxis.max]
      : [Math.min(...allData.map(d => d.x)), Math.max(...allData.map(d => d.x))];
    
    return scaleLinear({
      range: [0, innerWidth],
      domain,
    });
  }, [allData, innerWidth, params.xAxis]);

  const yScale = useMemo(() => {
    const domain = params.yAxis?.min !== undefined && params.yAxis?.max !== undefined
      ? [params.yAxis.min, params.yAxis.max]
      : [Math.min(...allData.map(d => d.y)), Math.max(...allData.map(d => d.y))];
    
    return scaleLinear({
      range: [innerHeight, 0],
      domain,
    });
  }, [allData, innerHeight, params.yAxis]);

  const colorScale = scaleOrdinal({
    domain: series.map(s => s.id),
    range: ['#0077ff', '#ff3366', '#00cc88', '#ff9900', '#9966cc', '#ff6699'],
  });

  // Get curve function based on series type
  const getCurve = (type: string) => {
    switch (type) {
      case 'spline': return curveCardinal;
      case 'step': return curveStepAfter;
      default: return curveMonotoneX;
    }
  };

  // Handle mouse events
  const handleMouseMove = useCallback((event: React.MouseEvent) => {
    if (!params.tooltip?.enabled) return;
    
    const point = localPoint(event);
    if (!point) return;

    const x = xScale.invert(point.x - margin.left);
    const y = yScale.invert(point.y - margin.top);

    // Find closest data point
    let closestPoint: DataPoint | null = null;
    let minDistance = Infinity;

    series.forEach(seriesItem => {
      if (seriesItem.visible === false) return;
      
      seriesItem.data.forEach(dataPoint => {
        const distance = Math.sqrt(
          Math.pow(xScale(dataPoint.x) - (point.x - margin.left), 2) +
          Math.pow(yScale(dataPoint.y) - (point.y - margin.top), 2)
        );
        
        if (distance < minDistance && distance < 20) {
          minDistance = distance;
          closestPoint = dataPoint;
        }
      });
    });

    if (closestPoint) {
      showTooltip({
        tooltipData: closestPoint,
        tooltipLeft: point.x,
        tooltipTop: point.y,
      });
      
      onInteractionUpdate({ hoveredPoint: closestPoint });
    } else {
      hideTooltip();
      onInteractionUpdate({ hoveredPoint: null });
    }
  }, [xScale, yScale, series, margin, showTooltip, hideTooltip, onInteractionUpdate, params.tooltip]);

  const handleMouseLeave = useCallback(() => {
    hideTooltip();
    onInteractionUpdate({ hoveredPoint: null });
  }, [hideTooltip, onInteractionUpdate]);

  const renderSeries = (seriesItem: GraphSeries) => {
    if (seriesItem.visible === false) return null;
    
    const color = seriesItem.color || colorScale(seriesItem.id);
    const data = seriesItem.data;

    switch (seriesItem.type) {
      case 'line':
      case 'spline':
      case 'step':
        return (
          <motion.g
            key={seriesItem.id}
            initial={{ pathLength: 0, opacity: 0 }}
            animate={{ pathLength: 1, opacity: 1 }}
            transition={{ 
              duration: seriesItem.animationDuration || 1,
              delay: seriesItem.animationDelay || 0,
              ease: 'easeInOut'
            }}
          >
            <LinePath
              data={data}
              x={d => xScale(d.x)}
              y={d => yScale(d.y)}
              stroke={color}
              strokeWidth={seriesItem.strokeWidth || 2}
              curve={getCurve(seriesItem.type)}
            />
          </motion.g>
        );

      case 'area':
        return (
          <motion.g
            key={seriesItem.id}
            initial={{ opacity: 0, scaleY: 0 }}
            animate={{ opacity: 1, scaleY: 1 }}
            transition={{ 
              duration: seriesItem.animationDuration || 1,
              delay: seriesItem.animationDelay || 0,
            }}
          >
            <AreaClosed
              data={data}
              x={d => xScale(d.x)}
              y={d => yScale(d.y)}
              yScale={yScale}
              fill={color}
              fillOpacity={seriesItem.fillOpacity || 0.3}
              stroke={color}
              strokeWidth={seriesItem.strokeWidth || 2}
              curve={getCurve(seriesItem.type)}
            />
          </motion.g>
        );

      case 'scatter':
        return (
          <motion.g
            key={seriesItem.id}
            initial={{ opacity: 0, scale: 0 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ 
              duration: seriesItem.animationDuration || 1,
              delay: seriesItem.animationDelay || 0,
            }}
          >
            {data.map((point, i) => (
              <circle
                key={i}
                cx={xScale(point.x)}
                cy={yScale(point.y)}
                r={point.size || 4}
                fill={point.color || color}
                stroke={color}
                strokeWidth={1}
              />
            ))}
          </motion.g>
        );

      case 'bar':
        const barWidth = innerWidth / data.length * 0.8;
        return (
          <motion.g
            key={seriesItem.id}
            initial={{ opacity: 0, scaleY: 0 }}
            animate={{ opacity: 1, scaleY: 1 }}
            transition={{ 
              duration: seriesItem.animationDuration || 1,
              delay: seriesItem.animationDelay || 0,
            }}
          >
            {data.map((point, i) => (
              <Bar
                key={i}
                x={xScale(point.x) - barWidth / 2}
                y={yScale(point.y)}
                width={barWidth}
                height={innerHeight - yScale(point.y)}
                fill={point.color || color}
              />
            ))}
          </motion.g>
        );

      default:
        return null;
    }
  };

  const GraphContent = ({ zoom }: { zoom?: any }) => (
    <Group left={margin.left} top={margin.top}>
      <RectClipPath id="chart-clip" width={innerWidth} height={innerHeight} />
      
      {/* Grid */}
      {gridVisible && (
        <GridColumns
          scale={xScale}
          height={innerHeight}
          stroke={params.xAxis?.gridColor || '#e0e0e0'}
          strokeOpacity={params.xAxis?.gridOpacity || 0.3}
        />
      )}
      {gridVisible && (
        <GridRows
          scale={yScale}
          width={innerWidth}
          stroke={params.yAxis?.gridColor || '#e0e0e0'}
          strokeOpacity={params.yAxis?.gridOpacity || 0.3}
        />
      )}

      {/* Series */}
      <g clipPath="url(#chart-clip)">
        {zoom ? (
          <g transform={zoom.toString()}>
            {series.map(renderSeries)}
          </g>
        ) : (
          series.map(renderSeries)
        )}
      </g>

      {/* Axes */}
      <AxisBottom
        top={innerHeight}
        scale={xScale}
        label={params.xAxis?.label}
        labelProps={{
          fontSize: params.xAxis?.labelFontSize || 12,
          fill: params.xAxis?.labelColor || '#333',
          textAnchor: 'middle',
        }}
        tickFormat={params.xAxis?.tickFormat}
        numTicks={params.xAxis?.tickCount}
      />
      <AxisLeft
        scale={yScale}
        label={params.yAxis?.label}
        labelProps={{
          fontSize: params.yAxis?.labelFontSize || 12,
          fill: params.yAxis?.labelColor || '#333',
          textAnchor: 'middle',
        }}
        tickFormat={params.yAxis?.tickFormat}
        numTicks={params.yAxis?.tickCount}
      />
    </Group>
  );

  return (
    <div style={{ position: 'relative' }}>
      <svg width={width} height={height}>
        {params.mode === 'interactive' && params.interaction?.zoom ? (
          <Zoom
            width={innerWidth}
            height={innerHeight}
            scaleXMin={0.1}
            scaleXMax={10}
            scaleYMin={0.1}
            scaleYMax={10}
          >
            {(zoom) => (
              <rect
                width={width}
                height={height}
                fill={params.backgroundColor || 'transparent'}
                onMouseMove={handleMouseMove}
                onMouseLeave={handleMouseLeave}
                onTouchStart={zoom.dragStart}
                onTouchMove={zoom.dragMove}
                onTouchEnd={zoom.dragEnd}
                onMouseDown={zoom.dragStart}
                onMouseMove={zoom.dragMove}
                onMouseUp={zoom.dragEnd}
                onWheel={zoom.wheelDelta}
              >
                <GraphContent zoom={zoom.transformMatrix} />
              </rect>
            )}
          </Zoom>
        ) : (
          <rect
            width={width}
            height={height}
            fill={params.backgroundColor || 'transparent'}
            onMouseMove={handleMouseMove}
            onMouseLeave={handleMouseLeave}
          >
            <GraphContent />
          </rect>
        )}
      </svg>

      {/* Tooltip */}
      {tooltipOpen && tooltipData && params.tooltip?.enabled !== false && (
        <TooltipWithBounds
          top={tooltipTop}
          left={tooltipLeft}
          style={{
            ...defaultStyles,
            backgroundColor: params.tooltip?.backgroundColor || 'rgba(0, 0, 0, 0.8)',
            color: params.tooltip?.textColor || 'white',
            fontSize: params.tooltip?.fontSize || 12,
            border: `1px solid ${params.tooltip?.borderColor || '#ccc'}`,
          }}
        >
          <div>
            {params.tooltip?.format ? 
              params.tooltip.format.replace('%{x}', tooltipData.x.toString()).replace('%{y}', tooltipData.y.toString()) :
              `(${tooltipData.x}, ${tooltipData.y})`
            }
            {tooltipData.label && <div>{tooltipData.label}</div>}
          </div>
        </TooltipWithBounds>
      )}
    </div>
  );
};

export default VisxGraph;
