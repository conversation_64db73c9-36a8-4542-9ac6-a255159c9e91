import React, { useEffect, useRef, useState } from 'react';
import Plotly from 'plotly.js-dist-min';
import { GraphDiagramParams, GraphSeries, DataPoint } from '../../../diagramTypes';

interface PlotlyGraphProps {
  params: GraphDiagramParams;
  series: GraphSeries[];
  margin: { top: number; right: number; bottom: number; left: number };
  interactionState: any;
  onSeriesUpdate: (series: GraphSeries[]) => void;
  onInteractionUpdate: (update: any) => void;
}

const PlotlyGraph: React.FC<PlotlyGraphProps> = ({
  params,
  series,
  margin,
  interactionState,
  onSeriesUpdate,
  onInteractionUpdate,
}) => {
  const plotRef = useRef<HTMLDivElement>(null);
  const [plotlyInstance, setPlotlyInstance] = useState<any>(null);

  const getPlotlyTraceType = (type: string) => {
    switch (type) {
      case 'line': return 'scatter';
      case 'scatter': return 'scatter';
      case 'bar': return 'bar';
      case 'area': return 'scatter';
      case 'spline': return 'scatter';
      case 'step': return 'scatter';
      default: return 'scatter';
    }
  };

  const getPlotlyMode = (type: string) => {
    switch (type) {
      case 'line': return 'lines';
      case 'scatter': return 'markers';
      case 'area': return 'lines';
      case 'spline': return 'lines';
      case 'step': return 'lines';
      default: return 'lines+markers';
    }
  };

  const getPlotlyLine = (seriesItem: GraphSeries) => {
    const line: any = {
      color: seriesItem.color || '#0077ff',
      width: seriesItem.strokeWidth || 2,
    };

    if (seriesItem.type === 'spline') {
      line.shape = 'spline';
    } else if (seriesItem.type === 'step') {
      line.shape = 'hv';
    }

    return line;
  };

  const createPlotlyData = () => {
    return series.filter(s => s.visible !== false).map((seriesItem) => {
      const trace: any = {
        x: seriesItem.data.map(d => d.x),
        y: seriesItem.data.map(d => d.y),
        type: getPlotlyTraceType(seriesItem.type),
        mode: getPlotlyMode(seriesItem.type),
        name: seriesItem.name,
        line: getPlotlyLine(seriesItem),
        marker: {
          color: seriesItem.color || '#0077ff',
          size: seriesItem.data.map(d => d.size || 6),
        },
        hovertemplate: params.tooltip?.format || '%{x}, %{y}<extra></extra>',
      };

      if (seriesItem.type === 'area') {
        trace.fill = 'tonexty';
        trace.fillcolor = seriesItem.color ? 
          `rgba(${hexToRgb(seriesItem.color)}, ${seriesItem.fillOpacity || 0.3})` : 
          'rgba(0, 119, 255, 0.3)';
      }

      if (seriesItem.type === 'bar') {
        trace.marker.color = seriesItem.color || '#0077ff';
      }

      return trace;
    });
  };

  const createPlotlyLayout = () => {
    const layout: any = {
      width: (params.width as number) || 800,
      height: (params.height as number) || 400,
      margin: {
        l: margin.left,
        r: margin.right,
        t: margin.top,
        b: margin.bottom,
      },
      paper_bgcolor: params.backgroundColor || 'transparent',
      plot_bgcolor: params.backgroundColor || 'transparent',
      font: {
        color: params.customTheme?.textColor || '#333',
        size: 12,
      },
      xaxis: {
        title: params.xAxis?.label || '',
        showgrid: params.xAxis?.gridVisible !== false,
        gridcolor: params.xAxis?.gridColor || '#e0e0e0',
        gridwidth: 1,
        range: params.xAxis?.min !== undefined && params.xAxis?.max !== undefined ? 
          [params.xAxis.min, params.xAxis.max] : undefined,
        tickformat: params.xAxis?.tickFormat || '',
        nticks: params.xAxis?.tickCount || undefined,
      },
      yaxis: {
        title: params.yAxis?.label || '',
        showgrid: params.yAxis?.gridVisible !== false,
        gridcolor: params.yAxis?.gridColor || '#e0e0e0',
        gridwidth: 1,
        range: params.yAxis?.min !== undefined && params.yAxis?.max !== undefined ? 
          [params.yAxis.min, params.yAxis.max] : undefined,
        tickformat: params.yAxis?.tickFormat || '',
        nticks: params.yAxis?.tickCount || undefined,
      },
      showlegend: params.legend?.visible !== false,
      legend: {
        orientation: params.legend?.orientation || 'v',
        x: params.legend?.position?.includes('right') ? 1.02 : 
           params.legend?.position?.includes('left') ? -0.1 : 1.02,
        y: params.legend?.position?.includes('top') ? 1 : 
           params.legend?.position?.includes('bottom') ? 0 : 0.5,
        bgcolor: params.legend?.backgroundColor || 'rgba(255,255,255,0.8)',
        bordercolor: params.legend?.borderColor || '#ccc',
        borderwidth: 1,
        font: {
          color: params.legend?.textColor || '#333',
          size: params.legend?.fontSize || 12,
        },
      },
      hovermode: params.tooltip?.enabled !== false ? 'closest' : false,
    };

    return layout;
  };

  const createPlotlyConfig = () => {
    const config: any = {
      responsive: true,
      displayModeBar: params.mode === 'interactive',
      modeBarButtonsToRemove: params.mode === 'plot' ? ['toImage'] : [],
      scrollZoom: params.interaction?.zoom && params.mode === 'interactive',
      doubleClick: params.interaction?.zoom ? 'reset' : false,
    };

    if (params.mode === 'plot') {
      config.staticPlot = true;
    }

    return config;
  };

  // Handle interaction updates
  useEffect(() => {
    if (!plotlyInstance) return;

    if (interactionState.resetZoom && interactionState.resetZoom !== null) {
      Plotly.relayout(plotlyInstance, {
        'xaxis.autorange': true,
        'yaxis.autorange': true
      });
    }

    if (interactionState.toggleGrid && interactionState.toggleGrid !== null) {
      const gridVisible = interactionState.gridVisible;
      Plotly.relayout(plotlyInstance, {
        'xaxis.showgrid': gridVisible,
        'yaxis.showgrid': gridVisible
      });
    }
  }, [interactionState.resetZoom, interactionState.toggleGrid, interactionState.gridVisible, plotlyInstance]);

  useEffect(() => {
    if (!plotRef.current) return;

    const data = createPlotlyData();
    const layout = createPlotlyLayout();
    const config = createPlotlyConfig();

    Plotly.newPlot(plotRef.current, data, layout, config).then((plot) => {
      setPlotlyInstance(plot);

      if (params.mode === 'interactive') {
        // Add event listeners for interactions
        plot.on('plotly_hover', (eventData: any) => {
          const point = eventData.points[0];
          onInteractionUpdate({
            hoveredPoint: {
              x: point.x,
              y: point.y,
              seriesId: point.data.name,
            }
          });
        });

        plot.on('plotly_unhover', () => {
          onInteractionUpdate({ hoveredPoint: null });
        });

        plot.on('plotly_selected', (eventData: any) => {
          if (eventData && eventData.points) {
            const selectedPoints = eventData.points.map((p: any) => ({
              x: p.x,
              y: p.y,
              seriesId: p.data.name,
            }));
            onInteractionUpdate({ selection: selectedPoints });
          }
        });

        plot.on('plotly_relayout', (eventData: any) => {
          if (eventData['xaxis.range[0]'] !== undefined) {
            onInteractionUpdate({
              zoom: {
                x: [eventData['xaxis.range[0]'], eventData['xaxis.range[1]']],
                y: [eventData['yaxis.range[0]'], eventData['yaxis.range[1]']],
              }
            });
          }
        });
      }
    });

    return () => {
      if (plotRef.current) {
        Plotly.purge(plotRef.current);
      }
    };
  }, [params, series]);

  // Helper function to convert hex to rgb
  const hexToRgb = (hex: string) => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? 
      `${parseInt(result[1], 16)}, ${parseInt(result[2], 16)}, ${parseInt(result[3], 16)}` : 
      '0, 119, 255';
  };

  return <div ref={plotRef} style={{ width: '100%', height: '100%' }} />;
};

export default PlotlyGraph;
