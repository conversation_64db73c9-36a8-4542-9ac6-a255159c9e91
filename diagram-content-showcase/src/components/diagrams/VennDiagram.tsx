import React, { useEffect, useRef } from "react";
import * as d3 from "d3";
// Use the pre-built or dist version so that references to fmin are handled:
import * as venn from "venn.js/build/venn.js";
import { VennDiagramParams } from "../../diagramTypes";

interface Params {
  params: VennDiagramParams
}

export default function VennDiagramDiv({ params }: Params) {
  useEffect(() => {
    const chart = venn.VennDiagram();
    d3.select("#venn").datum(params.data).call(chart);
  }, [params]);

  return <div id="venn"></div>;
};

