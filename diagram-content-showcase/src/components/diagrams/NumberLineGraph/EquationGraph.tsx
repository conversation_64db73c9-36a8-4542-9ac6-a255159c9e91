// EquationGraph.tsx
import React, {
  useEffect,
  useMemo,
  useRef,
  useState,
  useImperativeHandle,
  forwardRef,
  PointerEvent as ReactPointerEvent,
} from 'react';
import { create, all, EvalFunction } from 'mathjs';
import { line as d3Line, curveCatmullRom } from 'd3-shape';
import { scaleLinear } from 'd3-scale';
import { select } from 'd3-selection';
import {
  zoom,
  zoomIdentity,
  ZoomTransform,
  ZoomBehavior,
} from 'd3-zoom';

const math = create(all);

const PAD = 48;
const SAMPLE_POINTS = 240;
const TICK_STEP = 2;

interface EquationGraphProps {
  equation: string;
  width?: number;
  height?: number;
  equalAspect?: boolean;
  xDomain?: [number, number];
  yRange?: [number, number];
}

const DEFAULT_X_DOMAIN: [number, number] = [-10, 10];
const DEFAULT_Y_RANGE: [number, number] = [-10, 10];

const EquationGraph = forwardRef<
  { resetZoom: () => void },
  EquationGraphProps
>(({ equation, width = 640, height = 420, equalAspect = false, xDomain: controlledXDomain, yRange: controlledYRange }, ref) => {
  const svgRef = useRef<SVGSVGElement>(null);
  const zoomRef = useRef<ZoomBehavior<SVGSVGElement, unknown> | null>(null);

  const initialXDomain = controlledXDomain ?? DEFAULT_X_DOMAIN;
  const yRange = controlledYRange ?? DEFAULT_Y_RANGE;

  const [compiledFn, setCompiledFn] = useState<EvalFunction>(() => math.compile('x'));
  const [xDomain, setXDomain] = useState<[number, number]>(initialXDomain);
  const [xVal, setXVal] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const dragging = useRef(false);

  useEffect(() => {
    try {
      setCompiledFn(() => math.compile(equation));
      setXDomain(initialXDomain);
      setXVal(0);
    } catch (err) {
      console.error('Invalid equation:', err);
    }
  }, [equation, initialXDomain]);

  const plotW = equalAspect ? Math.min(width - 2 * PAD, height - 2 * PAD) : width - 2 * PAD;
  const plotH = equalAspect ? plotW : height - 2 * PAD;

  const baseXScale = useMemo(() => scaleLinear().domain(initialXDomain).range([PAD, PAD + plotW]), [initialXDomain, plotW]);
  const xScale = useMemo(() => scaleLinear().domain(xDomain).range([PAD, PAD + plotW]), [xDomain, plotW]);
  const yScale = useMemo(() => scaleLinear().domain(yRange).range([height - PAD, height - PAD - plotH]), [plotH, height, yRange]);
  const invX = (sx: number) => xScale.invert(sx);

  const f = (x: number) => {
    try {
      const y = compiledFn.evaluate({ x });
      return Number.isFinite(y) ? y : NaN;
    } catch {
      return NaN;
    }
  };

  const pathD = useMemo(() => {
    const pts: [number, number][] = [];
    for (let i = 0; i <= SAMPLE_POINTS; i++) {
      const x = xDomain[0] + (i / SAMPLE_POINTS) * (xDomain[1] - xDomain[0]);
      const y = f(x);
      if (Number.isFinite(y)) pts.push([xScale(x), yScale(y)]);
    }
    return d3Line<[number, number]>().curve(curveCatmullRom.alpha(0.5))(pts) || '';
  }, [compiledFn, xDomain, xScale, yScale]);

  const xTicks = useMemo(() => {
    const start = Math.ceil(xDomain[0] / TICK_STEP) * TICK_STEP;
    const arr: number[] = [];
    for (let v = start; v <= xDomain[1]; v += TICK_STEP) arr.push(v);
    return arr;
  }, [xDomain]);

  const yTicks = useMemo(() => {
    const start = Math.ceil(yRange[0] / TICK_STEP) * TICK_STEP;
    const arr: number[] = [];
    for (let v = start; v <= yRange[1]; v += TICK_STEP) arr.push(v);
    return arr;
  }, [yRange]);

  useEffect(() => {
    if (!svgRef.current) return;
    const svgSel = select(svgRef.current);

    zoomRef.current = zoom<SVGSVGElement, unknown>()
      .scaleExtent([0.5, 20])
      .translateExtent([[PAD, -Infinity], [PAD + plotW, Infinity]])
      .on('zoom', (event) => {
        const t: ZoomTransform = event.transform;
        setXDomain(t.rescaleX(baseXScale).domain() as [number, number]);
      });

    svgSel.call(zoomRef.current as any);

    svgSel.on('dblclick.zoom', null);
    svgSel.on('dblclick', () =>
      svgSel.transition().duration(400).call((zoomRef.current as any).transform, zoomIdentity)
    );
  }, [baseXScale, plotW]);

  useImperativeHandle(ref, () => ({
    resetZoom: () => {
      if (svgRef.current && zoomRef.current) {
        select(svgRef.current).transition().duration(400).call((zoomRef.current as any).transform, zoomIdentity);
      }
    },
  }));

  const onPointerDown = (e: ReactPointerEvent) => {
    dragging.current = true;
    setIsDragging(true);
    svgRef.current?.setPointerCapture(e.pointerId);
    e.stopPropagation();
  };
  const onPointerMove = (e: ReactPointerEvent) => {
    if (!dragging.current || !svgRef.current) return;
    const box = svgRef.current.getBoundingClientRect();
    const viewX = ((e.clientX - box.left) / box.width) * width;
    const clamped = Math.max(PAD, Math.min(PAD + plotW, viewX));
    setXVal(invX(clamped));
  };
  const onPointerUp = (e: ReactPointerEvent) => {
    dragging.current = false;
    setIsDragging(false);
    svgRef.current?.releasePointerCapture(e.pointerId);
  };

  useEffect(() => {
    if (xVal < xDomain[0] || xVal > xDomain[1]) {
      setXVal(Math.min(Math.max(xVal, xDomain[0]), xDomain[1]));
    }
  }, [xDomain, xVal]);

  const cx = xScale(xVal);
  const cy = yScale(f(xVal));
  const axisX = Math.max(PAD, Math.min(PAD + plotW, xScale(0)));

  return (
    <svg
      ref={svgRef}
      viewBox={`0 0 ${width} ${height}`}
      width="100%"
      height="100%"
      style={{
        background: '#fafafa',
        borderRadius: 12,
        boxShadow: '0 1px 4px rgba(0,0,0,.08)',
        userSelect: 'none',
        cursor: isDragging ? 'grabbing' : 'default',
      }}
      onPointerMove={onPointerMove}
      onPointerUp={onPointerUp}
    >
      <defs>
        <clipPath id="plotClip">
          <rect x={PAD} y={height - PAD - plotH} width={plotW} height={plotH} />
        </clipPath>
      </defs>

      <line x1={PAD} x2={PAD + plotW} y1={yScale(0)} y2={yScale(0)} stroke="#555" />
      <line x1={axisX} x2={axisX} y1={height - PAD - plotH} y2={height - PAD} stroke="#555" />

      {xTicks.map((v) => (
        <g key={`x-${v}`} style={{ pointerEvents: 'none' }}>
          <line x1={xScale(v)} x2={xScale(v)} y1={yScale(0) - 4} y2={yScale(0) + 4} stroke="#555" />
          <text x={xScale(v)} y={yScale(0) + 16} fontSize={9} textAnchor="middle" fill="#333" style={{ userSelect: 'none' }}>{v}</text>
        </g>
      ))}

      {yTicks.map((v) => (
        <g key={`y-${v}`} style={{ pointerEvents: 'none' }}>
          <line x1={axisX - 4} x2={axisX + 4} y1={yScale(v)} y2={yScale(v)} stroke="#555" />
          <text x={axisX - 8} y={yScale(v) + 4} fontSize={9} textAnchor="end" fill="#333" style={{ userSelect: 'none' }}>{v}</text>
        </g>
      ))}

      <path d={pathD} fill="none" stroke="#0077ff" strokeWidth={2} clipPath="url(#plotClip)" />

      <circle
        cx={cx}
        cy={cy}
        r={6}
        fill="#ff3366"
        style={{ cursor: isDragging ? 'grabbing' : 'grab' }}
        onPointerDown={onPointerDown}
      />

      <text x={cx + 10} y={cy - 10} fontSize={8} fill="#222" style={{ pointerEvents: 'none', userSelect: 'none' }}>
        ({xVal.toFixed(2)}, {f(xVal).toFixed(2)})
      </text>
    </svg>
  );
});

export default EquationGraph;