// EquationEditorWrapper.tsx
import React, { useState, useRef } from 'react';
import EquationGraph from './EquationGraph';
import { create, all } from 'mathjs';

const math = create(all);

const supportedFunctions = ['sin', 'cos', 'tan', 'log', 'sqrt', 'exp'];
const operators = ['+', '-', '*', '/', '^'];

const StyledButton: React.FC<React.ButtonHTMLAttributes<HTMLButtonElement>> = ({ children, ...props }) => (
    <button
        {...props}
        style={{
            background: '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: 4,
            padding: '6px 10px',
            fontSize: 14,
            cursor: 'pointer',
            transition: 'background 0.2s',
        }}
        onMouseEnter={(e) => ((e.target as HTMLElement).style.background = '#0056b3')}
        onMouseLeave={(e) => ((e.target as HTMLElement).style.background = '#007bff')}
    >
        {children}
    </button>
);

const ToggleButton: React.FC<{
    active: boolean;
    onClick: () => void;
    children: React.ReactNode;
}> = ({ active, onClick, children }) => (
    <button
        onClick={onClick}
        style={{
            background: active ? '#007bff' : '#e6e6e6',
            color: active ? '#fff' : '#333',
            border: 'none',
            padding: '6px 12px',
            fontSize: 12,
            fontWeight: 500,
            borderRadius: 4,
            marginRight: 4,
            cursor: 'pointer',
        }}
    >
        {children}
    </button>
);


const InputLabel: React.FC<{ label: string; value: number; onChange: (val: number) => void }> = ({ label, value, onChange }) => (
    <div style={{ display: 'flex', flexDirection: 'column', fontSize: 12 }}>
        <span style={{ marginBottom: 2, color: '#555' }}>{label}</span>
        <input
            type="number"
            value={value}
            onChange={(e) => onChange(Number(e.target.value))}
            style={{ padding: 4, borderRadius: 4, border: '1px solid #ccc', width: 80 }}
        />
    </div>
);

const EquationEditorWrapper: React.FC = () => {
    const [mode, setMode] = useState<'free' | 'guided'>('free');
    const [equation, setEquation] = useState('x');
    const [error, setError] = useState<string | null>(null);
    const [guidedExpression, setGuidedExpression] = useState('');

    const [xMin, setXMin] = useState(-10);
    const [xMax, setXMax] = useState(10);
    const [yMin, setYMin] = useState(-10);
    const [yMax, setYMax] = useState(10);
    const [autoFitY, setAutoFitY] = useState(false);

    const graphRef = useRef<{ resetZoom: () => void }>(null);

    const validateEquation = (eq: string) => {
        try {
            const compiled = math.compile(eq);
            compiled.evaluate({ x: 1 });
            setError(null);
            return true;
        } catch (err: any) {
            setError(err.message);
            return false;
        }
    };

    const handleFreeFormChange = (val: string) => {
        setEquation(val);
        validateEquation(val);
    };

    const appendToGuided = (val: string) => {
        const updated = guidedExpression + val;
        setGuidedExpression(updated);
        setEquation(updated);
        validateEquation(updated);
    };

    const resetGuided = () => {
        setGuidedExpression('');
        setEquation('');
        setError(null);
    };

    const getAutoYRange = () => {
        try {
            const compiled = math.compile(equation);
            const N = 240;
            const dx = (xMax - xMin) / N;
            let minY = Infinity;
            let maxY = -Infinity;
            for (let i = 0; i <= N; i++) {
                const x = xMin + i * dx;
                const y = compiled.evaluate({ x });
                if (Number.isFinite(y)) {
                    minY = Math.min(minY, y);
                    maxY = Math.max(maxY, y);
                }
            }
            const padding = (maxY - minY) * 0.1 || 1;
            return [minY - padding, maxY + padding];
        } catch {
            return [yMin, yMax];
        }
    };

    const effectiveYRange = autoFitY ? getAutoYRange() : [yMin, yMax];

    return (
        <div style={{ maxWidth: 800, margin: '0 auto', fontFamily: 'sans-serif' }}>
            <div style={{ display: 'flex', marginBottom: 12 }}>
                <ToggleButton active={mode === 'free'} onClick={() => setMode('free')}>
                    Free-form
                </ToggleButton>
                <ToggleButton active={mode === 'guided'} onClick={() => setMode('guided')}>
                    Guided
                </ToggleButton>
            </div>


            {mode === 'free' && (
                <div style={{ marginBottom: 16 }}>
                    <label>
                        Equation:{' '}
                        <input
                            value={equation}
                            onChange={(e) => handleFreeFormChange(e.target.value)}
                            style={{ fontFamily: 'monospace', width: '100%', padding: 8, border: '1px solid #ccc', borderRadius: 4 }}
                        />
                    </label>
                    <div style={{ fontSize: 12, color: '#666', marginTop: 4 }}>
                        Example: <code>sin(x) + x^2</code>
                    </div>
                </div>
            )}

            {mode === 'guided' && (
                <div style={{ marginBottom: 16 }}>
                    <input
                        value={guidedExpression}
                        onChange={(e) => {
                            const val = e.target.value;
                            setGuidedExpression(val);
                            setEquation(val);
                            validateEquation(val);
                        }}
                        placeholder="Type numbers and use buttons below"
                        style={{ fontFamily: 'monospace', width: '100%', padding: 8, border: '1px solid #ccc', borderRadius: 4, marginBottom: 8 }}
                    />

                    <div style={{ display: 'flex', flexWrap: 'wrap', gap: 8, marginBottom: 8 }}>
                        <StyledButton onClick={() => appendToGuided('x')}>x</StyledButton>
                        {supportedFunctions.map((fn) => (
                            <StyledButton key={fn} onClick={() => appendToGuided(`${fn}(`)}>{fn}(</StyledButton>
                        ))}
                        {operators.map((op) => (
                            <StyledButton key={op} onClick={() => appendToGuided(op)}>{op}</StyledButton>
                        ))}
                        <StyledButton onClick={() => appendToGuided(')')}>)</StyledButton>
                        <StyledButton onClick={resetGuided}>Clear</StyledButton>
                    </div>
                </div>
            )}

            <div style={{ display: 'flex', gap: 16, marginBottom: 16, flexWrap: 'wrap', alignItems: 'flex-end' }}>
                <InputLabel label="X Min" value={xMin} onChange={setXMin} />
                <InputLabel label="X Max" value={xMax} onChange={setXMax} />
                {!autoFitY && <>
                    <InputLabel label="Y Min" value={yMin} onChange={setYMin} />
                    <InputLabel label="Y Max" value={yMax} onChange={setYMax} />
                </>}
                <label style={{ display: 'flex', alignItems: 'center', gap: 6, fontSize: 12 }}>
                    <input type="checkbox" checked={autoFitY} onChange={(e) => setAutoFitY(e.target.checked)} /> Auto-fit Y-axis
                </label>
            </div>

            <div style={{ marginBottom: 12 }}>
                <button
                    className='hover:bg-gray-200 bg-zinc-500 hover:text-black text-white'
                    onClick={() => graphRef.current?.resetZoom()}
                    style={{
                        padding: '4px 10px',
                        borderRadius: 4,
                        border: '1px solid #ccc',
                        fontSize: 12,
                        cursor: 'pointer',
                    }}
                >
                    🔄 Reset Zoom
                </button>

            </div>

            {error && (
                <div style={{ color: 'red', marginBottom: 16 }}><strong>Error:</strong> {error}</div>
            )}

            {!error && equation && (
                <EquationGraph
                    ref={graphRef}
                    equation={equation}
                    width={640}
                    height={420}
                    xDomain={[xMin, xMax]}
                    yRange={effectiveYRange as [number, number]}
                />
            )}
        </div>
    );
};

export default EquationEditorWrapper;
