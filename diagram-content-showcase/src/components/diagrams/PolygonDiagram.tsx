import React, { useEffect, useRef } from "react";
import * as d3 from "d3";
import { animate } from "animejs"; // Anime v4
import { interpolate } from "flubber";
import { PolygonDiagramParams } from "../../diagramTypes";

interface PolygonDiagramProps {
  params: PolygonDiagramParams & {
    pointLabels?: string[];
    lineLabels?: string[];
  };
  size?: number;
  normalized?: boolean;
}

const PolygonDiagram: React.FC<PolygonDiagramProps> = ({
  params,
  size = 400,
  normalized = true,
}) => {
  const svgRef = useRef<SVGSVGElement | null>(null);
  const prevPath = useRef<string | null>(null);
  const prevPoints = useRef<{ x: number; y: number }[] | null>(null);
  const prevFill = useRef<string | null>(null);
  const prevStroke = useRef<string | null>(null);

  const pointsToPath = (pts: { x: number; y: number }[]) =>
    pts.length
      ? `M${pts[0].x},${pts[0].y} ` +
        pts.slice(1).map(p => `L${p.x},${p.y}`).join(" ") +
        " Z"
      : "";

  useEffect(() => {
    if (!svgRef.current || !params.points?.length) return;

    const SCALE = 200, EXTRA = 20;
    const drawW = (!normalized && params.width! > 1)
      ? params.width! : SCALE * (params.width ?? 1);
    const drawH = (!normalized && params.height! > 1)
      ? params.height! : SCALE * (params.height ?? 1);

    const totalW = drawW + EXTRA, totalH = drawH + EXTRA;
    const offX = (totalW - drawW) / 2, offY = (totalH - drawH) / 2;

    const svg = d3
      .select(svgRef.current)
      .attr("viewBox", `0 0 ${totalW} ${totalH}`)
      .attr("preserveAspectRatio", "xMidYMid meet")
      .style("width", "100%")
      .style("height", "100%");

    let g = svg.select<SVGGElement>("g#diagram-group");
    if (g.empty()) g = svg.append("g").attr("id", "diagram-group");
    g.attr("transform", `translate(${offX}, ${offY})`);

    const MARGIN = 10;
    const sx = d3.scaleLinear().domain([0, 1]).range([MARGIN, drawW - MARGIN]);
    const sy = d3.scaleLinear().domain([0, 1]).range([MARGIN, drawH - MARGIN]);

    const pts = params.points.map(p => ({ x: sx(p.x), y: sy(p.y) }));
    const newPath = pointsToPath(pts);
    const n = pts.length;

    const newFill = params.fill ?? "none";
    const newStroke = params.stroke ?? "black";

    let path = g.select<SVGPathElement>("path#polygon-path");
    if (path.empty()) {
      path = g.append("path")
        .attr("id", "polygon-path")
        .attr("fill", newFill)
        .attr("stroke", newStroke)
        .attr("stroke-width", params.strokeWidth ?? 2)
        .attr("d", newPath);
      prevPath.current = newPath;
      prevPoints.current = pts;
      prevFill.current = newFill;
      prevStroke.current = newStroke;
    } else if (prevPath.current !== newPath) {
      const interpPath = interpolate(prevPath.current!, newPath);
      const oldPts = prevPoints.current ?? pts;
      const dummy = { t: 0 };

      const fromFill = d3.color(prevFill.current ?? "none")!.formatRgb();
      const toFill = d3.color(newFill)!.formatRgb();
      const fromStroke = d3.color(prevStroke.current ?? "black")!.formatRgb();
      const toStroke = d3.color(newStroke)!.formatRgb();

      animate(dummy, {
        t: 1,
        duration: 700,
        ease: "inOutQuad",
        onUpdate: () => {
          const t = dummy.t;
          path.attr("d", interpPath(t));

          const fillColor = d3.interpolateRgb(fromFill, toFill)(t);
          const strokeColor = d3.interpolateRgb(fromStroke, toStroke)(t);
          path.attr("fill", fillColor).attr("stroke", strokeColor);

          const centroid = {
            x: d3.mean(pts, p => p.x)!,
            y: d3.mean(pts, p => p.y)!,
          };

          pts.forEach((next, i) => {
            const prev = oldPts[i] ?? next;
            const x = prev.x + (next.x - prev.x) * t;
            const y = prev.y + (next.y - prev.y) * t;
            const dir = { x: x - centroid.x, y: y - centroid.y };
            const len = Math.hypot(dir.x, dir.y) || 1;
            const labelX = x + (dir.x / len) * 10;
            const labelY = y + (dir.y / len) * 10;

            g.select(`text.point-label-${i}`)
              .attr("x", labelX)
              .attr("y", labelY);
          });

          pts.forEach((next, i) => {
            const p1Prev = oldPts[i] ?? next;
            const p2Prev = oldPts[(i + 1) % oldPts.length] ?? next;
            const p1New = pts[i];
            const p2New = pts[(i + 1) % pts.length];

            const x1 = p1Prev.x + (p1New.x - p1Prev.x) * t;
            const y1 = p1Prev.y + (p1New.y - p1Prev.y) * t;
            const x2 = p2Prev.x + (p2New.x - p2Prev.x) * t;
            const y2 = p2Prev.y + (p2New.y - p2Prev.y) * t;

            const midX = (x1 + x2) / 2;
            const midY = (y1 + y2) / 2;
            const dx = x2 - x1, dy = y2 - y1;
            const len = Math.hypot(dx, dy) || 1;
            const normal = { x: dy / len, y: -dx / len };
            const labelX = midX + normal.x * 20;
            const labelY = midY + normal.y * 20;

            g.select(`text.line-label-${i}`)
              .attr("x", labelX)
              .attr("y", labelY);
          });
        },
        onComplete: () => {
          prevPath.current = newPath;
          prevPoints.current = pts;
          prevFill.current = newFill;
          prevStroke.current = newStroke;
        },
      });
    }

    g.selectAll("text").remove();

    const centroid = {
      x: d3.mean(pts, p => p.x)!,
      y: d3.mean(pts, p => p.y)!,
    };

    pts.forEach((p, i) => {
      const dir = { x: p.x - centroid.x, y: p.y - centroid.y };
      const len = Math.hypot(dir.x, dir.y) || 1;
      const x = p.x + (dir.x / len) * 10;
      const y = p.y + (dir.y / len) * 10;

      g.append("text")
        .attr("x", x)
        .attr("y", y)
        .attr("text-anchor", "middle")
        .attr("class", `point-label point-label-${i}`)
        .style("font-size", "7px")
        .style("font-weight", "700")
        .text(params.pointLabels?.[i] ?? "");
    });

    pts.forEach((p1, i) => {
      const p2 = pts[(i + 1) % n];
      const midX = (p1.x + p2.x) / 2;
      const midY = (p1.y + p2.y) / 2;

      const dx = p2.x - p1.x, dy = p2.y - p1.y;
      const len = Math.hypot(dx, dy);
      if (len === 0) return;

      const normal = { x: dy / len, y: -dx / len };
      const x = midX + normal.x * 20;
      const y = midY + normal.y * 20;

      g.append("text")
        .attr("x", x)
        .attr("y", y)
        .attr("text-anchor", "middle")
        .attr("class", `line-label line-label-${i}`)
        .style("font-size", "7px")
        .text(params.lineLabels?.[i] ?? "");
    });
  }, [params.points, normalized, params.width, params.height, params.fill, params.stroke]);

  return (
    <div style={{ width: `${size}px`, height: `${size}px`, aspectRatio: 1 }}>
      <svg ref={svgRef} />
    </div>
  );
};

export default PolygonDiagram;
