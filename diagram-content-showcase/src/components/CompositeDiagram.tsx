// CompositeDiagram.tsx
import React from "react";
import { AnimatePresence, motion } from "framer-motion";
import {
  AngleDiagramParams,
  DiagramDefinition,
  PolygonDiagramParams,
  VennDiagramParams,
  GraphDiagramParams,
} from "../diagramTypes";
import VennDiagram from "./diagrams/VennDiagram";
import PolygonDiagram from "./diagrams/PolygonDiagram";
import AngleDiagram from "./diagrams/AngleDiagram";
import GraphComponent from "./diagrams/GraphComponent";

interface CompositeDiagramProps {
  definition: DiagramDefinition;
}

const CompositeDiagram: React.FC<CompositeDiagramProps> = ({ definition }) => {
  return (
    <AnimatePresence mode="wait">
      <motion.div
        // key={definition.diagramType + JSON.stringify(definition)} // ensures full remount on change
        key={definition.diagramType} // ensures full remount on change
        initial={{ opacity: 0, scale: 1.05, filter: "blur(4px)" }}
        animate={{ opacity: 1, scale: 1, filter: "blur(0px)" }}
        exit={{ opacity: 0, scale: 0.95, filter: "blur(4px)" }}
        transition={{ duration: 0.6, ease: "easeInOut" }}
      >
        {definition.diagramType === "venn" && (
          <VennDiagram params={definition as VennDiagramParams} />
        )}
        {definition.diagramType === "polygon" && (
          <PolygonDiagram params={definition as PolygonDiagramParams} />
        )}
        {definition.diagramType === "angle" && (
          <AngleDiagram params={definition as AngleDiagramParams} />
        )}
        {definition.diagramType === "graph" && (
          <GraphComponent params={definition as GraphDiagramParams} />
        )}
      </motion.div>
    </AnimatePresence>
  );
};

export default CompositeDiagram;
