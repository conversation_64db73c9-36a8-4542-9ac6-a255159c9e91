import React, { useState } from "react";
import AngleDiagram from "../components/diagrams/AngleDiagram";

const AngleDiagramPage: React.FC = () => {
  const [from, setFrom] = useState(0);
  const [to, setTo] = useState(60);

  return (
    <div className="flex flex-col items-center">
      <h2 className="text-xl font-bold mb-6">Interactive Angle Diagram</h2>
      
      <div className="mb-6">
        <div className="flex gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium mb-1">From Angle: {from}°</label>
            <input 
              type="range" 
              min="0" 
              max="359" 
              value={from} 
              onChange={(e) => setFrom(parseInt(e.target.value))}
              className="w-full"
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">To Angle: {to}°</label>
            <input 
              type="range" 
              min="0" 
              max="359" 
              value={to} 
              onChange={(e) => setTo(parseInt(e.target.value))}
              className="w-full"
            />
          </div>
        </div>
        <p className="text-sm text-gray-600 mb-4">Drag the handles to adjust the angle interactively</p>
      </div>
      
      <div className="border rounded-lg p-4 bg-white shadow-sm">
        <AngleDiagram
          params={{
            diagramType: "angle",
            fromAngleDeg: from,
            toAngleDeg: to,
            draggable: "both",
            onAngleChange: ({ fromAngleDeg, toAngleDeg }) => {
              if (fromAngleDeg !== undefined) setFrom(fromAngleDeg);
              if (toAngleDeg !== undefined) setTo(toAngleDeg);
            },
            labels: { _from: "A", _vertex: "B", _to: "C" },
            arcColor: "red",
            labelColor: "black",
            highlightColor: "blue",
            lineColor: "gray"
          }}
          size={400}
        />
      </div>
    </div>
  );
};

export default AngleDiagramPage;