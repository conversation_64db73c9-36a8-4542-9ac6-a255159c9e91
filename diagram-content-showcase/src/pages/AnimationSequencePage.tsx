import React from "react";
import AnimatedDiagramSequence from "../components/AnimatedDiagramSequence";

const AnimationSequencePage: React.FC = () => {
  return (
    <div className="flex flex-col items-center">
      <h2 className="text-xl font-bold mb-6">Basic Animation Sequence</h2>
      
      <div className="w-full max-w-3xl border rounded-lg p-4 bg-white shadow-sm flex justify-center items-center">
        <div className="w-full max-w-md">
          <AnimatedDiagramSequence
            loop
            sequence={[
              {
                diagramType: "venn",
                data: [
                  { sets: ["A"], size: 10, label: "Group A" },
                  { sets: ["B"], size: 10, label: "Group B" },
                  { sets: ["A", "B"], size: 5, label: "Overlap" },
                ],
                duration: 3000,
              },
              {
                diagramType: "polygon",
                width: 1,
                height: 1,
                points: [
                  { x: 0.5, y: 0.1 },
                  { x: 0.7828, y: 0.2172 },
                  { x: 0.9, y: 0.5 },
                  { x: 0.7828, y: 0.7828 },
                  { x: 0.5, y: 0.9 },
                  { x: 0.2172, y: 0.7828 },
                  { x: 0.1, y: 0.5 },
                  { x: 0.2172, y: 0.2172 },
                ],
                fill: "#f5f5f5",
                stroke: "black",
                strokeWidth: 1,
                lineLabels: Array(8).fill("8cm"),
                duration: 2500,
              },
              {
                diagramType: "polygon",
                width: 1,
                height: 1,
                points: [
                  { x: 0.5, y: 0.1 },    // Top vertex
                  { x: 0.9, y: 0.9 },    // Bottom right
                  { x: 0.1, y: 0.9 },    // Bottom left
                ],
                fill: "#d0ebff",
                stroke: "black",
                strokeWidth: 1,
                lineLabels: ["1cm", "1cm", "1cm"],
                duration: 2500,
              },
              {
                diagramType: "angle",
                fromAngleDeg: 0,
                toAngleDeg: 60,
                labels: { _vertex: "B", _from: "A", _to: "C" },
                angleLabel: "θ",
                showHandles: false,
                duration: 2000,
              },
            ]}
          />
        </div>
      </div>
    </div>
  );
};

export default AnimationSequencePage;
