import React from "react";
import AnimatedDiagramSequenceWithDynamicTransitions from "../components/AnimatedDiagramSequenceWithDynamicTransitions";

const DynamicTransitionsPage: React.FC = () => {
  return (
    <div className="flex flex-col items-center">
      <h2 className="text-xl font-bold mb-6">Dynamic Transitions</h2>
      
      <div className="w-full max-w-3xl border rounded-lg p-4 bg-white shadow-sm">
        <AnimatedDiagramSequenceWithDynamicTransitions
          loop
          sequence={[
            {
              diagramType: "venn",
              data: [
                { sets: ["A"], size: 10, label: "A" },
                { sets: ["B"], size: 10, label: "B" },
                { sets: ["A", "B"], size: 5, label: "A ∩ B" },
              ],
              duration: 4000,
              transitionType: "distortFade",
            },
            {
              diagramType: "polygon",
              width: 1,
              height: 1,
              points: [
                { x: 0.5, y: 0.1 },
                { x: 0.9, y: 0.9 },
                { x: 0.1, y: 0.9 },
              ],
              fill: "#e6f7ff",
              stroke: "black",
              strokeWidth: 1,
              lineLabels: ["1cm", "1cm", "1cm"],
              duration: 4000,
              transitionType: "flip",
            },
            {
              diagramType: "angle",
              fromAngleDeg: 0,
              toAngleDeg: 45,
              labels: { _from: "A", _vertex: "B", _to: "C" },
              angleLabel: "θ",
              showHandles: false,
              duration: 4000,
              transitionType: "explode",
            },
            {
              diagramType: "polygon",
              width: 1,
              height: 1,
              points: [
                { x: 0.5, y: 0.1 },
                { x: 0.7828, y: 0.2172 },
                { x: 0.9, y: 0.5 },
                { x: 0.7828, y: 0.7828 },
                { x: 0.5, y: 0.9 },
                { x: 0.2172, y: 0.7828 },
                { x: 0.1, y: 0.5 },
                { x: 0.2172, y: 0.2172 },
              ],
              fill: "#fff5e6",
              stroke: "black",
              strokeWidth: 1,
              lineLabels: Array(8).fill("8cm"),
              duration: 4000,
              transitionType: "ripple",
            },
            {
              diagramType: "venn",
              data: [
                { sets: ["X"], size: 15, label: "X" },
                { sets: ["Y"], size: 12, label: "Y" },
                { sets: ["X", "Y"], size: 3, label: "X ∩ Y" },
              ],
              duration: 4000,
              transitionType: "drop",
            },
            {
              diagramType: "angle",
              fromAngleDeg: 0,
              toAngleDeg: 90,
              labels: { _vertex: "O", _from: "A", _to: "B" },
              angleLabel: "90°",
              showHandles: false,
              duration: 4000,
              transitionType: "rotate",
            },
            {
              diagramType: "polygon",
              width: 1,
              height: 1,
              points: [
                { x: 0.1, y: 0.2 },
                { x: 0.9, y: 0.2 },
                { x: 0.9, y: 0.8 },
                { x: 0.1, y: 0.8 },
              ],
              fill: "#e6ffe6",
              stroke: "black",
              strokeWidth: 1,
              lineLabels: ["2cm", "1cm", "2cm", "1cm"],
              duration: 4000,
              transitionType: "warp",
            },
            {
              diagramType: "polygon",
              width: 1,
              height: 1,
              points: [
                { x: 0.5, y: 0.1 },
                { x: 0.7828, y: 0.2172 },
                { x: 0.9, y: 0.5 },
                { x: 0.7828, y: 0.7828 },
                { x: 0.5, y: 0.9 },
                { x: 0.2172, y: 0.7828 },
                { x: 0.1, y: 0.5 },
                { x: 0.2172, y: 0.2172 },
              ],
              fill: "#f5f5f5",
              stroke: "black",
              strokeWidth: 1,
              lineLabels: Array(8).fill("8cm"),
              duration: 4000,
              transitionType: "distortFade",
            },
            {
              diagramType: "angle",
              fromAngleDeg: 0,
              toAngleDeg: 60,
              labels: { _vertex: "B", _from: "A", _to: "C" },
              angleLabel: "θ",
              showHandles: false,
              duration: 4000,
              transitionType: "diagonal",
            },
          ]}
        />
      </div>
    </div>
  );
};

export default DynamicTransitionsPage;