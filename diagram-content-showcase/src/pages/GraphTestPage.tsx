import React, { useState } from 'react';
import GraphComponent from '../components/diagrams/GraphComponent';
import { GraphDiagramParams } from '../diagramTypes';

const GraphTestPage: React.FC = () => {
  const [debugInfo, setDebugInfo] = useState<any>({});

  const testGraphParams: GraphDiagramParams = {
    diagramType: "graph",
    mode: "interactive",
    width: 800,
    height: 500,
    series: [
      {
        id: "test-sine",
        name: "Sine Wave",
        data: Array.from({ length: 100 }, (_, i) => ({
          x: i * 0.1,
          y: Math.sin(i * 0.1),
        })),
        type: "line",
        color: "#0077ff",
        strokeWidth: 3,
        animated: true,
        animationDuration: 1500,
      },
      {
        id: "test-cosine",
        name: "Cosine Wave",
        data: Array.from({ length: 100 }, (_, i) => ({
          x: i * 0.1,
          y: Math.cos(i * 0.1),
        })),
        type: "line",
        color: "#ff3366",
        strokeWidth: 3,
        animated: true,
        animationDuration: 1500,
        animationDelay: 300,
      },
    ],
    xAxis: {
      label: "X (radians)",
      gridVisible: true,
      gridColor: "#e0e0e0",
      gridOpacity: 0.5,
      min: 0,
      max: 10,
    },
    yAxis: {
      label: "Y",
      gridVisible: true,
      gridColor: "#e0e0e0",
      gridOpacity: 0.5,
      min: -1.5,
      max: 1.5,
    },
    legend: {
      visible: true,
      position: "top-right",
      backgroundColor: "rgba(255, 255, 255, 0.9)",
    },
    tooltip: {
      enabled: true,
      format: "(%{x:.2f}, %{y:.2f})",
    },
    interaction: {
      zoom: true,
      pan: true,
      hover: true,
      crosshair: true,
    },
    animation: {
      enabled: true,
      duration: 1500,
      easing: "easeOut",
    },
    backgroundColor: "#fafafa",
  };

  const handleInteractionChange = (interaction: any) => {
    setDebugInfo(prev => ({
      ...prev,
      lastInteraction: {
        timestamp: new Date().toLocaleTimeString(),
        data: interaction
      }
    }));
    console.log('Interaction Update:', interaction);
  };

  const handleDataChange = (series: any) => {
    setDebugInfo(prev => ({
      ...prev,
      lastDataChange: {
        timestamp: new Date().toLocaleTimeString(),
        seriesCount: series.length
      }
    }));
    console.log('Data Change:', series);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-800 mb-2">Graph Component Test</h1>
          <p className="text-gray-600">
            Test page for verifying reset zoom and grid toggle functionality.
          </p>
        </div>

        {/* Test Instructions */}
        <div className="mb-6 p-4 bg-blue-50 rounded-lg">
          <h3 className="text-lg font-semibold text-blue-800 mb-2">Test Instructions:</h3>
          <ol className="text-blue-700 space-y-1">
            <li>1. <strong>Zoom Test:</strong> Use mouse wheel to zoom in/out, then click "Reset" button</li>
            <li>2. <strong>Pan Test:</strong> Click and drag to pan, then click "Reset" button</li>
            <li>3. <strong>Grid Test:</strong> Click "Grid" button to toggle grid on/off multiple times</li>
            <li>4. <strong>Persistence Test:</strong> After reset, zoom again - it should stay zoomed</li>
          </ol>
        </div>

        {/* Debug Information */}
        <div className="mb-6 p-4 bg-gray-100 rounded-lg">
          <h3 className="text-lg font-semibold text-gray-800 mb-2">Debug Information:</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <strong>Last Interaction:</strong>
              {debugInfo.lastInteraction ? (
                <div className="mt-1 p-2 bg-white rounded">
                  <div><strong>Time:</strong> {debugInfo.lastInteraction.timestamp}</div>
                  <div><strong>Data:</strong> {JSON.stringify(debugInfo.lastInteraction.data, null, 2)}</div>
                </div>
              ) : (
                <div className="mt-1 text-gray-500">No interactions yet</div>
              )}
            </div>
            <div>
              <strong>Last Data Change:</strong>
              {debugInfo.lastDataChange ? (
                <div className="mt-1 p-2 bg-white rounded">
                  <div><strong>Time:</strong> {debugInfo.lastDataChange.timestamp}</div>
                  <div><strong>Series Count:</strong> {debugInfo.lastDataChange.seriesCount}</div>
                </div>
              ) : (
                <div className="mt-1 text-gray-500">No data changes yet</div>
              )}
            </div>
          </div>
        </div>

        {/* Graph Component */}
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          <div className="p-4 bg-gray-50 border-b">
            <h3 className="text-lg font-semibold text-gray-800">Interactive Graph with Reset & Grid Toggle</h3>
            <p className="text-sm text-gray-600 mt-1">
              Click the settings icon (⚙️) in the top-right corner of the graph to access controls.
            </p>
          </div>
          
          <div className="p-4">
            <GraphComponent 
              params={testGraphParams}
              onDataChange={handleDataChange}
              onInteractionChange={handleInteractionChange}
            />
          </div>
        </div>

        {/* Expected Behavior */}
        <div className="mt-6 p-4 bg-green-50 rounded-lg">
          <h3 className="text-lg font-semibold text-green-800 mb-2">Expected Behavior:</h3>
          <div className="text-green-700 space-y-2">
            <div><strong>✅ Reset Button:</strong> Should reset zoom/pan and stay reset (no auto-revert)</div>
            <div><strong>✅ Grid Toggle:</strong> Should toggle grid on/off and remember state</div>
            <div><strong>✅ Visual Feedback:</strong> Reset button shows spinning icon, Grid button shows On/Off state</div>
            <div><strong>✅ Persistence:</strong> After reset, new zoom/pan operations should work normally</div>
          </div>
        </div>

        {/* Browser Console */}
        <div className="mt-6 p-4 bg-yellow-50 rounded-lg">
          <h3 className="text-lg font-semibold text-yellow-800 mb-2">Browser Console:</h3>
          <p className="text-yellow-700">
            Open browser developer tools (F12) and check the console for detailed interaction logs.
            All interaction updates and data changes are logged for debugging.
          </p>
        </div>
      </div>
    </div>
  );
};

export default GraphTestPage;
