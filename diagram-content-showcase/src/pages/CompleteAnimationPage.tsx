import React from "react";
import CompleteAnimatedDiagramSequence from "../components/CompleteAnimatedDiagramSequence";

const CompleteAnimationPage: React.FC = () => {
  return (
    <div className="flex flex-col items-center">
      <h2 className="text-xl font-bold mb-6">Complete Animation with Audio</h2>
      
      <div className="w-full max-w-3xl border rounded-lg p-4 bg-white shadow-sm">
        <CompleteAnimatedDiagramSequence
          playOnRender={true}
          sequence={[
            {
              diagramType: "venn",
              data: [
                { sets: ["A"], size: 10, label: "A" },
                { sets: ["B"], size: 10, label: "B" },
              ],
              duration: 4000,
              transitionType: "distortFade",
              audioUrl: "/set1.mp3"
            },
            {
              diagramType: "venn",
              data: [
                { sets: ["A"], size: 10, label: "A" },
                { sets: ["B"], size: 10, label: "B" },
                { sets: ["A", "B"], size: 5, label: "A ∩ B" },
              ],
              duration: 4000,
              transitionType: "zoom",
              audioUrl: "/set2.mp3"
            },
            {
              diagramType: "polygon",
              width: 1,
              height: 1,
              points: [
                { x: 0.5, y: 0.1 },
                { x: 0.9, y: 0.9 },
                { x: 0.1, y: 0.9 },
              ],
              fill: "#d0ebff",
              stroke: "black",
              strokeWidth: 1,
              lineLabels: ["1cm", "1cm", "1cm"],
              duration: 4000,
              transitionType: "flip",
              audioUrl: "/triangle.mp3"
            },
            {
              diagramType: "angle",
              fromAngleDeg: 0,
              toAngleDeg: 60,
              labels: { _vertex: "B", _from: "A", _to: "C" },
              angleLabel: "θ",
              showHandles: false,
              duration: 4000,
              transitionType: "rotate",
              audioUrl: "/angle.mp3",
              animationType: "infinitePulse"
            }
          ]}
        />
      </div>
    </div>
  );
};

export default CompleteAnimationPage;
