import React from 'react';
import InteractiveEquationGraph from '../components/diagrams/graphs/InteractiveEquationGraph';

const InteractiveEquationPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-800 mb-2">Interactive Equation Grapher</h1>
          <p className="text-gray-600">
            A comprehensive graphing tool supporting mathematical functions, parametric equations, polar coordinates,
            and number lines with real-time equation input and interactive controls.
          </p>
        </div>

        <InteractiveEquationGraph 
          width={1000}
          height={700}
          initialType="function"
          initialEquation="sin(x) + cos(2*x)"
        />

        <div className="mt-8 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-3">Function Graphs</h3>
            <p className="text-gray-600 text-sm mb-3">
              Plot mathematical functions like y = f(x). Supports trigonometric, exponential, 
              logarithmic, and polynomial functions.
            </p>
            <div className="space-y-1 text-xs text-gray-500">
              <div><strong>Examples:</strong></div>
              <div>• sin(x), cos(x), tan(x)</div>
              <div>• x^2, x^3, sqrt(x)</div>
              <div>• log(x), exp(x), abs(x)</div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-3">Parametric Equations</h3>
            <p className="text-gray-600 text-sm mb-3">
              Plot parametric curves defined by x(t) and y(t). Perfect for creating 
              complex curves and animations.
            </p>
            <div className="space-y-1 text-xs text-gray-500">
              <div><strong>Examples:</strong></div>
              <div>• cos(t), sin(t) (circle)</div>
              <div>• t, t^2 (parabola)</div>
              <div>• t*cos(t), t*sin(t) (spiral)</div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-3">Polar Coordinates</h3>
            <p className="text-gray-600 text-sm mb-3">
              Create beautiful polar plots with r = f(θ). Great for roses, 
              cardioids, and other symmetric patterns.
            </p>
            <div className="space-y-1 text-xs text-gray-500">
              <div><strong>Examples:</strong></div>
              <div>• 1 + cos(theta) (cardioid)</div>
              <div>• sin(2*theta) (rose)</div>
              <div>• theta (spiral)</div>
            </div>
          </div>



          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-3">Number Lines</h3>
            <p className="text-gray-600 text-sm mb-3">
              Interactive number line for teaching number concepts, inequalities, 
              and mathematical sequences.
            </p>
            <div className="space-y-1 text-xs text-gray-500">
              <div><strong>Format:</strong></div>
              <div>• Comma-separated numbers</div>
              <div>• Example: -5, -2, 0, 3, 7</div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-3">Interactive Features</h3>
            <p className="text-gray-600 text-sm mb-3">
              Full interactivity with zoom, pan, real-time equation editing, 
              and customizable ranges.
            </p>
            <div className="space-y-1 text-xs text-gray-500">
              <div><strong>Controls:</strong></div>
              <div>• Zoom and pan with mouse</div>
              <div>• Adjustable domain/range</div>
              <div>• Variable resolution</div>
              <div>• Animation controls</div>
            </div>
          </div>
        </div>

        <div className="mt-8 bg-blue-50 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-blue-800 mb-3">Tips for Best Results</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-700">
            <div>
              <h4 className="font-medium mb-2">Function Syntax:</h4>
              <ul className="space-y-1">
                <li>• Use standard mathematical notation</li>
                <li>• Multiplication: 2*x (not 2x)</li>
                <li>• Powers: x^2, x^3</li>
                <li>• Functions: sin(x), cos(x), log(x)</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">Performance Tips:</h4>
              <ul className="space-y-1">
                <li>• Lower resolution for complex functions</li>
                <li>• Adjust domain for better visualization</li>
                <li>• Use appropriate ranges to avoid clipping</li>
                <li>• Try the quick examples for inspiration</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InteractiveEquationPage;
