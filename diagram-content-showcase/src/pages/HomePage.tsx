import React, { useState } from "react";
import CompositeDiagram from "../components/CompositeDiagram";
import { getDiagramDefinitionFromPrompt } from "../diagramGenerator";
import { DiagramDefinition } from "../diagramTypes";

const HomePage: React.FC = () => {
  const [prompt, setPrompt] = useState("");
  const [diagramDefinition, setDiagramDefinition] = 
    useState<DiagramDefinition | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  const handleGenerateDiagram = async () => {
    setLoading(true)
    try {
      const definition = await getDiagramDefinitionFromPrompt(prompt);
      console.log(definition)
      setDiagramDefinition(definition);
      setError(null);
    } catch (err) {
      setError("Error generating diagram. Check the console for details.");
      console.log(err)
    } finally {
      setLoading(false)
    }
  };

  return (
    <div className="flex flex-col md:flex-row w-full gap-6">
      <div className="flex flex-col gap-4 items-start md:w-1/3">
        <h2 className="text-xl font-bold">Diagram Generator</h2>
        <textarea
          value={prompt}
          onChange={(e) => setPrompt(e.target.value)}
          placeholder="Enter a diagram description..."
          className="w-full p-3 border border-gray-300 rounded-lg"
          rows={6}
        />
        {
          loading ?
            <button className="bg-teal-500/80 cursor-pointer text-white rounded-lg h-[2.5rem] flex items-center px-3">Loading...</button> :
            <button className="bg-teal-500 hover:bg-teal-600 text-[0.9rem] cursor-pointer text-white rounded-lg h-[2.5rem] flex items-center px-3" onClick={handleGenerateDiagram}>Generate Diagram</button>
        }
      </div>
      <div className="flex-1 flex flex-col justify-center items-center min-h-[400px] border-2 border-dashed border-gray-200 rounded-lg">
        {error && <p className="text-red-500">{error}</p>}
        {diagramDefinition && (
          <div className="flex-1 flex items-center justify-center w-full">
            <CompositeDiagram definition={diagramDefinition} />
          </div>
        )}
        {!error && !diagramDefinition && (
          <p className="text-gray-400">Your generated diagram will appear here</p>
        )}
      </div>
    </div>
  );
};

export default HomePage;