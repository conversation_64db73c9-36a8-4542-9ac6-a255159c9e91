import React, { useState } from 'react';
import { generatePolygonDiagramData } from '../chains/polygon/polygonChain';
import PolygonDiagram from '../components/diagrams/PolygonDiagram';
import { PolygonDiagramParams } from '../diagramTypes';

const PolygonTestPage: React.FC = () => {
  const [prompt, setPrompt] = useState('');
  const [result, setResult] = useState<PolygonDiagramParams | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const testCases = [
    // Basic Shape Tests
    {
      name: "Square with flat bottom",
      prompt: "Draw a square"
    },
    {
      name: "Square with labeled vertices",
      prompt: "Draw a square and label the vertices A, B, C, D"
    },
    {
      name: "Rectangle",
      prompt: "Create a rectangle"
    },
    {
      name: "Rectangle with labeled sides",
      prompt: "Draw a rectangle and label the sides"
    },
    {
      name: "Triangle with specific vertex labels",
      prompt: "Create a triangle with vertices labeled P, Q, R"
    },
    {
      name: "Pentagon with equal sides",
      prompt: "Draw a pentagon with all sides labeled 5cm"
    },
    {
      name: "Hexagon with vertex and side labels",
      prompt: "Create a hexagon, label vertices A through F and label all sides"
    },
    {
      name: "Square with side measurements",
      prompt: "Draw a square with sides labeled 4cm each"
    },

    // Color Tests
    {
      name: "Red Triangle",
      prompt: "Give me a red triangle"
    },
    {
      name: "Blue Square with Labels",
      prompt: "Create a blue square with labeled vertices A, B, C, D"
    },
    {
      name: "Green Pentagon",
      prompt: "Draw a green pentagon"
    },
    {
      name: "Yellow Rectangle",
      prompt: "Make a yellow rectangle"
    },
    {
      name: "Purple Hexagon with Black Border",
      prompt: "Create a purple hexagon with black border"
    },
    {
      name: "Orange Triangle with Labels",
      prompt: "Draw an orange triangle and label the vertices X, Y, Z"
    },
    {
      name: "Pink Square with Blue Outline",
      prompt: "Create a pink square with blue outline"
    },
    {
      name: "Hex Code Color Test",
      prompt: "Draw a #FF6B6B triangle"
    },
    {
      name: "Multiple Colors Test",
      prompt: "Create a red rectangle with green border and label the sides"
    },
    {
      name: "Cyan Pentagon with Labels",
      prompt: "Make a cyan pentagon with vertices labeled A through E"
    }
  ];

  const handleTest = async (testPrompt: string) => {
    setLoading(true);
    setError(null);
    setPrompt(testPrompt);
    
    try {
      const diagramData = await generatePolygonDiagramData.invoke({ input: testPrompt });
      setResult(diagramData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      setResult(null);
    } finally {
      setLoading(false);
    }
  };

  const handleCustomTest = async () => {
    if (!prompt.trim()) return;
    await handleTest(prompt);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Polygon Generation Test Suite</h1>
        
        {/* Custom Input */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">Custom Test</h2>
          <div className="flex gap-4">
            <input
              type="text"
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              placeholder="Enter your polygon prompt..."
              className="flex-1 px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <button
              onClick={handleCustomTest}
              disabled={loading || !prompt.trim()}
              className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Testing...' : 'Test'}
            </button>
          </div>
        </div>

        {/* Predefined Test Cases */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">Predefined Test Cases</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {testCases.map((testCase, index) => (
              <button
                key={index}
                onClick={() => handleTest(testCase.prompt)}
                disabled={loading}
                className="p-4 text-left border border-gray-200 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <div className="font-medium text-gray-900">{testCase.name}</div>
                <div className="text-sm text-gray-600 mt-1">"{testCase.prompt}"</div>
              </button>
            ))}
          </div>
        </div>

        {/* Results */}
        {(result || error) && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Diagram Display */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold mb-4">Generated Diagram</h2>
              {error ? (
                <div className="text-red-600 p-4 bg-red-50 rounded-md">
                  Error: {error}
                </div>
              ) : result ? (
                <div className="flex justify-center">
                  <PolygonDiagram params={result} size={300} />
                </div>
              ) : null}
            </div>

            {/* Data Display */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold mb-4">Generated Data</h2>
              {result && (
                <div className="space-y-4">
                  <div>
                    <h3 className="font-medium text-gray-700">Prompt:</h3>
                    <p className="text-sm text-gray-600 bg-gray-50 p-2 rounded">"{prompt}"</p>
                  </div>
                  
                  <div>
                    <h3 className="font-medium text-gray-700">Points ({result.points.length}):</h3>
                    <pre className="text-xs bg-gray-50 p-2 rounded overflow-x-auto">
                      {JSON.stringify(result.points, null, 2)}
                    </pre>
                  </div>
                  
                  {result.pointLabels && (
                    <div>
                      <h3 className="font-medium text-gray-700">Point Labels:</h3>
                      <p className="text-sm bg-gray-50 p-2 rounded">
                        [{result.pointLabels.map(label => `"${label}"`).join(', ')}]
                      </p>
                    </div>
                  )}
                  
                  {result.lineLabels && (
                    <div>
                      <h3 className="font-medium text-gray-700">Line Labels:</h3>
                      <p className="text-sm bg-gray-50 p-2 rounded">
                        [{result.lineLabels.map(label => `"${label}"`).join(', ')}]
                      </p>
                    </div>
                  )}

                  <div>
                    <h3 className="font-medium text-gray-700">Colors:</h3>
                    <div className="flex gap-4 text-sm">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">Fill:</span>
                        <div
                          className="w-6 h-6 border border-gray-300 rounded"
                          style={{ backgroundColor: result.fill }}
                        ></div>
                        <span className="text-gray-600">{result.fill}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">Stroke:</span>
                        <div
                          className="w-6 h-6 border-2 rounded"
                          style={{ borderColor: result.stroke, backgroundColor: 'transparent' }}
                        ></div>
                        <span className="text-gray-600">{result.stroke}</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="font-medium text-gray-700">Full Data:</h3>
                    <pre className="text-xs bg-gray-50 p-2 rounded overflow-x-auto max-h-40">
                      {JSON.stringify(result, null, 2)}
                    </pre>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PolygonTestPage;
