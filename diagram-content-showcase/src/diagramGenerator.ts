import { z } from "zod";
import { ChatOpenAI } from "@langchain/openai";
import {
  Runnable<PERSON><PERSON>b<PERSON>,
  RunnableBranch,
} from "@langchain/core/runnables";
import { PromptTemplate } from "@langchain/core/prompts";
import { DiagramDefinition } from "./diagramTypes";

import { extractNumSetsChain } from "./chains/venn/extractNumSetsChain";
import { generateVennUniqueSetsChain } from "./chains/venn/generateVennUniqueSetsChain";
import { generateVennOverlapsChain } from "./chains/venn/generateVennOverlapsChain";
import { generatePolygonDiagramData } from "./chains/polygon/polygonChain";
import { generateAngleDiagramData } from "./chains/angle/generateAngleDiagramData"; // ✅ Import angle chain
import { generateVennDiagramData } from "./chains/venn/generateVennDiagramData";
import { generateGraphDiagramData } from "./chains/graph/generateGraphDiagramData";

// 🌟 Initialize LLM
const chat = new ChatOpenAI({
  model: "gpt-4o-mini",
  apiKey: import.meta.env.VITE_OPENAI_API_KEY,
  temperature: 0,
});

// ────────────────────────────────────────────────
// 🔹 Zod Schemas
// ────────────────────────────────────────────────

const vennSchema = z.object({
  diagramType: z.literal("venn"),
  data: z.array(
    z.object({
      sets: z.array(z.string()),
      size: z.number(),
      label: z.string().optional(),
    })
  ),
});

const polygonSchema = z.object({
  diagramType: z.literal("polygon"),
  width: z.number(),
  height: z.number(),
  points: z.array(z.object({ x: z.number(), y: z.number() })),
  fill: z.string().optional(),
  stroke: z.string().optional(),
  strokeWidth: z.number().optional(),
  pointLabels: z.array(z.string()).optional(),
  lineLabels: z.array(z.string()).optional(),
});

const angleSchema = z.object({
  diagramType: z.literal("angle"),
  fromAngleDeg: z.number().optional(),
  toAngleDeg: z.number(),
  draggable: z.enum(["from", "to", "both", "none"]).optional(),
  snapIncrement: z.number().optional(),
  drawClockwise: z.boolean().optional(),
  arcColor: z.string().optional(),
  lineColor: z.string().optional(),
  labelColor: z.string().optional(),
  fontSize: z.number().optional(),
  highlightColor: z.string().optional(),
  showHandles: z.boolean().optional(),
  labels: z
    .object({
      _from: z.string().optional(),
      _vertex: z.string().optional(),
      _to: z.string().optional(),
    })
    .optional(),
  angleLabel: z.string().optional(),
});

const diagramUnionSchema = z.union([vennSchema, polygonSchema, angleSchema]);

// ────────────────────────────────────────────────
// 🔹 Route Selector
// ────────────────────────────────────────────────

const routePrompt = PromptTemplate.fromTemplate(
  `Analyze the prompt and determine the best diagram type:
- If it describes set relationships (overlapping, disjoint, intersections), return "venn".
- If it describes geometric shapes (vertices, edges), return "polygon".
- If it describes angles (degrees, radians, turning, arc, ∠ABC), return "angle".
- If it describes data visualization, charts, graphs, plots, functions, mathematical equations, line charts, bar charts, scatter plots, or any data representation, return "graph".

Return only one of: "venn", "polygon", "angle", "graph".

Prompt: {input}`
);

const routeSelector = routePrompt.pipe(chat).pipe(
  new RunnableLambda({
    func: async (response: any) => response.text.trim().toLowerCase(),
  })
);

// ────────────────────────────────────────────────
// 🔹 Multi-route Branch
// ────────────────────────────────────────────────

const multiRouteChain = RunnableBranch.from([
  [
    new RunnableLambda({
      func: async (input: any) => (await routeSelector.invoke(input)) === "venn",
    }),
    generateVennDiagramData as any,
  ],
  [
    new RunnableLambda({
      func: async (input: any) => (await routeSelector.invoke(input)) === "polygon",
    }),
    generatePolygonDiagramData as any,
  ],
  [
    new RunnableLambda({
      func: async (input: any) => (await routeSelector.invoke(input)) === "angle",
    }),
    generateAngleDiagramData as any,
  ],
  [
    new RunnableLambda({
      func: async (input: any) => (await routeSelector.invoke(input)) === "graph",
    }),
    generateGraphDiagramData as any,
  ],
  new RunnableLambda({
    func: async () => ({ error: "Unrecognized diagram type." }),
  }),
]);

// ────────────────────────────────────────────────
// 🔹 Final Exported Function
// ────────────────────────────────────────────────

export async function getDiagramDefinitionFromPrompt(prompt: string): Promise<DiagramDefinition> {
  const result = await multiRouteChain.invoke({ input: prompt });

  const parsed = diagramUnionSchema.safeParse(result);
  if (!parsed.success) {
    console.error("Invalid diagram structure:", parsed.error);
    throw new Error("Output did not match any valid diagram definition");
  }

  return parsed.data;
}
