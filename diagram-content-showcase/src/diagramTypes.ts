// diagramTypes.ts

export interface BaseDiagramParams {
  diagramType: string; // This property determines which diagram to render
}

interface VennSetItem {
  sets: string[];   // e.g. ["A"], or ["A", "B"] for intersection
  size: number;     // cardinality (the size of this region)
  label?: string;   // user-defined label to appear in that region
}

export interface VennDiagramParams extends BaseDiagramParams {
  diagramType: "venn";
  // Specific parameters for a Venn diagram
  data: VennSetItem[]
}

// diagramTypes.ts
export interface PolygonDiagramParams extends BaseDiagramParams {
  diagramType: "polygon";
  width: number;
  height: number;
  points: { x: number; y: number }[];
  fill?: string;
  stroke?: string;
  strokeWidth?: number;
  pointLabels?: string[];  // Optional: labels for each vertex.
  lineLabels?: string[];   // Optional: labels for each line segment.
}



export interface AngleLabel {
  label?: string;       // e.g., "<ABC", "θ", "A", "30°"
  angleDeg?: number;    // actual angle in degrees (if known)
  angleRad?: number;    // actual angle in radians (alternative)
  showDegreeSymbol?: boolean; // display with ° if true
  color?: string;       // optional label color
  fontSize?: number;    // optional font size override
}

export interface AngleDiagramParams extends BaseDiagramParams {
  diagramType: "angle";
  fromAngleDeg?: number;
  toAngleDeg: number;
  draggable?: "from" | "to" | "both" | "none";
  onAngleChange?: (update: { fromAngleDeg?: number; toAngleDeg?: number }) => void;
  snapIncrement?: number;
  drawClockwise?: boolean;
  arcColor?: string;
  lineColor?: string;
  labelColor?: string;
  fontSize?: number;
  labels?: { _from?: string; _vertex?: string; _to?: string };
  angleLabel?: string;
  highlightColor?: string;
  showHandles?: boolean;
}

export type TransitionType =
  | "fade"
  | "zoom"
  | "slide"
  | "flip"
  | "drop"
  | "rotate"
  | "warp"
  | "diagonal"
  | "distortFade"
  | "ripple"
  | "glitch"
  | "explode"
  | "orbit";

export type AnimationType =
  | "infiniteRotate"
  | "infiniteBounce"
  | "infinitePulse"
  | "infiniteWiggle"
  | "infiniteGlow";



// Extend with more diagram types as needed
// Graph-related types
export interface DataPoint {
  x: number;
  y: number;
  label?: string;
  color?: string;
  size?: number;
}

export interface GraphSeries {
  id: string;
  name: string;
  data: DataPoint[];
  type: 'line' | 'scatter' | 'bar' | 'area' | 'spline' | 'step';
  color?: string;
  strokeWidth?: number;
  fillOpacity?: number;
  visible?: boolean;
  animated?: boolean;
  animationDuration?: number;
  animationDelay?: number;
}

export interface GraphAxis {
  label?: string;
  min?: number;
  max?: number;
  tickCount?: number;
  tickFormat?: string;
  gridVisible?: boolean;
  gridColor?: string;
  gridOpacity?: number;
  labelColor?: string;
  labelFontSize?: number;
}

export interface GraphLegend {
  visible?: boolean;
  position?: 'top' | 'bottom' | 'left' | 'right' | 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  orientation?: 'horizontal' | 'vertical';
  backgroundColor?: string;
  borderColor?: string;
  textColor?: string;
  fontSize?: number;
}

export interface GraphTooltip {
  enabled?: boolean;
  format?: string;
  backgroundColor?: string;
  textColor?: string;
  borderColor?: string;
  fontSize?: number;
}

export interface GraphInteraction {
  zoom?: boolean;
  pan?: boolean;
  select?: boolean;
  brush?: boolean;
  crosshair?: boolean;
  hover?: boolean;
}

export interface GraphAnimation {
  enabled?: boolean;
  duration?: number;
  easing?: 'linear' | 'easeIn' | 'easeOut' | 'easeInOut' | 'circIn' | 'circOut' | 'circInOut' | 'backIn' | 'backOut' | 'backInOut';
  stagger?: number;
  onComplete?: () => void;
}

export interface GraphDiagramParams extends BaseDiagramParams {
  diagramType: "graph";
  mode: 'plot' | 'interactive';
  width?: number;
  height?: number;

  // Equation-based graphing (preferred approach per JSON reference)
  equation?: string;
  graphType?: 'function' | 'parametric' | 'polar' | 'numberline';
  domain?: { min: number; max: number };
  resolution?: number;

  // Data series approach (for discrete data points)
  series?: GraphSeries[];

  xAxis?: GraphAxis;
  yAxis?: GraphAxis;
  legend?: GraphLegend;
  tooltip?: GraphTooltip;
  interaction?: GraphInteraction;
  animation?: GraphAnimation;
  backgroundColor?: string;
  margin?: {
    top?: number;
    right?: number;
    bottom?: number;
    left?: number;
  };
  theme?: 'light' | 'dark' | 'custom';
  customTheme?: {
    backgroundColor?: string;
    textColor?: string;
    gridColor?: string;
    axisColor?: string;
  };
}

export type DiagramDefinition =
  | VennDiagramParams
  | PolygonDiagramParams
  | AngleDiagramParams
  | GraphDiagramParams;
