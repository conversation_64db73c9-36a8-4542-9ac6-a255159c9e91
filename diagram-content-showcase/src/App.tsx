// App.tsx
import React from "react";
import { Link, Route, Routes, useLocation } from "react-router";
import HomePage from "./pages/HomePage";
import AngleDiagramPage from "./pages/AngleDiagramPage";
import EquationEditorPage from "./pages/EquationEditorPage";
import AnimationSequencePage from "./pages/AnimationSequencePage";
import DynamicTransitionsPage from "./pages/DynamicTransitionsPage";
import CompleteAnimationPage from "./pages/CompleteAnimationPage";
import GraphExamplesPage from "./pages/GraphExamplesPage";
import InteractiveEquationPage from "./pages/InteractiveEquationPage";
import GraphTestPage from "./pages/GraphTestPage";
import PolygonTestPage from "./pages/PolygonTestPage";

const App: React.FC = () => {
  const location = useLocation();
  
  const isActive = (path: string) => location.pathname === path;
  
  return (
    <div className="min-h-screen bg-gray-50">
      <nav className="bg-gradient-to-r from-teal-600 to-teal-500 text-white shadow-md">
        <div className="container mx-auto px-4 py-3">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between">
            <div className="flex items-center mb-4 md:mb-0">
              <svg 
                className="w-8 h-8 mr-3" 
                viewBox="0 0 24 24" 
                fill="none" 
                xmlns="http://www.w3.org/2000/svg"
              >
                <path 
                  d="M12 2L2 7L12 12L22 7L12 2Z" 
                  stroke="currentColor" 
                  strokeWidth="2" 
                  strokeLinecap="round" 
                  strokeLinejoin="round" 
                />
                <path 
                  d="M2 17L12 22L22 17" 
                  stroke="currentColor" 
                  strokeWidth="2" 
                  strokeLinecap="round" 
                  strokeLinejoin="round" 
                />
                <path 
                  d="M2 12L12 17L22 12" 
                  stroke="currentColor" 
                  strokeWidth="2" 
                  strokeLinecap="round" 
                  strokeLinejoin="round" 
                />
              </svg>
              <h1 className="text-2xl font-bold">Diagram Builder</h1>
            </div>
            
            <ul className="flex flex-wrap gap-1 md:gap-2">
              <li>
                <Link 
                  to="/" 
                  className={`px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${
                    isActive('/') 
                      ? 'bg-white text-teal-700' 
                      : 'text-white hover:bg-teal-700 hover:bg-opacity-50'
                  }`}
                >
                  Home
                </Link>
              </li>
              <li>
                <Link 
                  to="/angle-diagram" 
                  className={`px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${
                    isActive('/angle-diagram') 
                      ? 'bg-white text-teal-700' 
                      : 'text-white hover:bg-teal-700 hover:bg-opacity-50'
                  }`}
                >
                  Angle Diagram
                </Link>
              </li>
              <li>
                <Link 
                  to="/equation-editor" 
                  className={`px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${
                    isActive('/equation-editor') 
                      ? 'bg-white text-teal-700' 
                      : 'text-white hover:bg-teal-700 hover:bg-opacity-50'
                  }`}
                >
                  Equation Editor
                </Link>
              </li>
              <li>
                <Link 
                  to="/animation-sequence" 
                  className={`px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${
                    isActive('/animation-sequence') 
                      ? 'bg-white text-teal-700' 
                      : 'text-white hover:bg-teal-700 hover:bg-opacity-50'
                  }`}
                >
                  Animation Sequence
                </Link>
              </li>
              <li>
                <Link 
                  to="/dynamic-transitions" 
                  className={`px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${
                    isActive('/dynamic-transitions') 
                      ? 'bg-white text-teal-700' 
                      : 'text-white hover:bg-teal-700 hover:bg-opacity-50'
                  }`}
                >
                  Dynamic Transitions
                </Link>
              </li>
              <li>
                <Link
                  to="/complete-animation"
                  className={`px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${
                    isActive('/complete-animation')
                      ? 'bg-white text-teal-700'
                      : 'text-white hover:bg-teal-700 hover:bg-opacity-50'
                  }`}
                >
                  Complete Animation
                </Link>
              </li>
              <li>
                <Link
                  to="/graph-examples"
                  className={`px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${
                    isActive('/graph-examples')
                      ? 'bg-white text-teal-700'
                      : 'text-white hover:bg-teal-700 hover:bg-opacity-50'
                  }`}
                >
                  Graph Examples
                </Link>
              </li>
              <li>
                <Link
                  to="/interactive-equation"
                  className={`px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${
                    isActive('/interactive-equation')
                      ? 'bg-white text-teal-700'
                      : 'text-white hover:bg-teal-700 hover:bg-opacity-50'
                  }`}
                >
                  Equation Grapher
                </Link>
              </li>
              <li>
                <Link
                  to="/graph-test"
                  className={`px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${
                    isActive('/graph-test')
                      ? 'bg-white text-teal-700'
                      : 'text-white hover:bg-teal-700 hover:bg-opacity-50'
                  }`}
                >
                  Graph Test
                </Link>
              </li>
              <li>
                <Link
                  to="/polygon-test"
                  className={`px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${
                    isActive('/polygon-test')
                      ? 'bg-white text-teal-700'
                      : 'text-white hover:bg-teal-700 hover:bg-opacity-50'
                  }`}
                >
                  Polygon Test
                </Link>
              </li>
            </ul>
          </div>
        </div>
      </nav>

      <div className="container mx-auto p-6">
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/angle-diagram" element={<AngleDiagramPage />} />
          <Route path="/equation-editor" element={<EquationEditorPage />} />
          <Route path="/animation-sequence" element={<AnimationSequencePage />} />
          <Route path="/dynamic-transitions" element={<DynamicTransitionsPage />} />
          <Route path="/complete-animation" element={<CompleteAnimationPage />} />
          <Route path="/graph-examples" element={<GraphExamplesPage />} />
          <Route path="/interactive-equation" element={<InteractiveEquationPage />} />
          <Route path="/graph-test" element={<GraphTestPage />} />
          <Route path="/polygon-test" element={<PolygonTestPage />} />
        </Routes>
      </div>
    </div>
  );
};

export default App;
