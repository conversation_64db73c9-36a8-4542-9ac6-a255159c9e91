import { Runnable<PERSON>amb<PERSON> } from "@langchain/core/runnables";
import { shapeIntentChain } from "./shapeIntentChain";
import { generatePolygonLabels<PERSON>hain } from "./generatePolygonLabels<PERSON>hain";
import { buildPolygonDiagram } from "./polygonDiagramBuilder";
import { PolygonDiagramParams } from "../../diagramTypes";

export const generatePolygonDiagramData = new RunnableLambda({
    func: async ({ input }: { input: string }): Promise<PolygonDiagramParams> => {
        const userPrompt = input;

        // Step 1: Extract shape intent (sides + label needs)
        console.log("🧠 Starting...");
        const shapeIntent = await shapeIntentChain.invoke({ input: userPrompt });
        console.log("🧠 Shape Intent:", shapeIntent);

        // Step 2: If labeling needed, call label chain
        let labelOutput = undefined;
        if (shapeIntent.shouldLabelPoints || shapeIntent.shouldLabelLines) {
            labelOutput = await generatePolygonLabelsChain.invoke({
                numberOfSides: shapeIntent.numberOfSides,
                shouldLabelPoints: shapeIntent.shouldLabelPoints,
                shouldLabelLines: shapeIntent.shouldLabelLines,
                prompt: userPrompt,
            });
            console.log("🏷️ Label Output:", labelOutput);
        } else {
            console.log("ℹ️ No labels requested.");
        }

        // Step 3: Build the final diagram data
        const diagram = await buildPolygonDiagram(userPrompt, shapeIntent, labelOutput);
        console.log("📐 Final Polygon Diagram:", diagram);

        return diagram;
    },
});
