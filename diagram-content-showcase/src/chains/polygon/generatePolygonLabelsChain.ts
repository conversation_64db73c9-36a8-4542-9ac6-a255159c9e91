// generatePolygonLabelsChain.ts
import { z } from "zod";
import { PromptTemplate } from "@langchain/core/prompts";
import { ChatOpenAI } from "@langchain/openai";

export const polygonLabelsSchema = z.object({
  pointLabels: z.array(z.string()).optional(),
  lineLabels: z.array(z.string()).optional(),
});

export type PolygonLabelOutput = z.infer<typeof polygonLabelsSchema>;

const polygonLabelsPrompt = PromptTemplate.fromTemplate(`
Based on the user's prompt, generate labels for a polygon diagram.

You are given:
- numberOfSides = {numberOfSides}
- shouldLabelPoints = {shouldLabelPoints}
- shouldLabelLines = {shouldLabelLines}

Extract and return the appropriate labels based on what the user explicitly requested.

Return JSON:
{{
  "pointLabels": [...],  // optional array of strings
  "lineLabels": [...]    // optional array of strings
}}

Guidelines for Point Labels (vertices):
- If user specifies exact labels like "label vertices A, B, C, D", extract those exact labels: ["A", "B", "C", "D"]
- If user says "label the vertices" without specifying, generate alphabetical: ["A", "B", "C", ...]
- If user mentions specific vertex names in context, use those
- Always return exactly {numberOfSides} labels if shouldLabelPoints is true

Guidelines for Line Labels (sides/edges):
- If user specifies measurements like "sides 3cm, 4cm, 5cm", extract those: ["3cm", "4cm", "5cm"]
- If user says "label the sides" without specifying, generate combinations of vertex labels: ["AB", "BC", "CA"]
- If user mentions "all sides equal" or similar, use the same label for all sides
- Always return exactly {numberOfSides} labels if shouldLabelLines is true

Special Cases:
- Look for patterns like "A, B, C, D" or "vertices A through D"
- Extract measurements with units: "3cm", "5 inches", "2.5m"
- Handle equal sides: "all sides 4cm" → ["4cm", "4cm", "4cm", "4cm"]

Important: Return empty arrays or omit fields if the corresponding should* flag is false.

Prompt: {prompt}
`);

export const generatePolygonLabelsChain = polygonLabelsPrompt.pipe(
  new ChatOpenAI({
    model: "gpt-4o-mini",
    apiKey: import.meta.env.VITE_OPENAI_API_KEY,
    temperature: 0,
  }).withStructuredOutput(polygonLabelsSchema)
);
