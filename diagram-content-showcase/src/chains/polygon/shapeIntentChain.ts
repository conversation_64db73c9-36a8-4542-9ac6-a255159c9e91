// shapeIntentChain.ts
import { z } from "zod";
import { ChatOpenAI } from "@langchain/openai";
import { PromptTemplate } from "@langchain/core/prompts";

export const shapeIntentSchema = z.object({
  numberOfSides: z.number(),
  shouldLabelPoints: z.boolean(),
  shouldLabelLines: z.boolean(),
  shapeType: z.enum(["regular", "rectangle", "square"]).optional(),
  fillColor: z.string().optional(),
  strokeColor: z.string().optional(),
});

export type ShapeIntent = z.infer<typeof shapeIntentSchema>;

const shapeIntentPrompt = PromptTemplate.fromTemplate(`
You are extracting polygon diagram intent from the user prompt.

Return JSON:
{{
  "numberOfSides": number,
  "shouldLabelPoints": boolean,
  "shouldLabelLines": boolean,
  "shapeType": "regular" | "rectangle" | "square" (optional),
  "fillColor": string (optional),
  "strokeColor": string (optional)
}}

Guidelines:
- Detect explicit labeling requests like "label vertices A, B, C, D", "label the sides", "with labels", etc.
- Set shouldLabelPoints to true if user mentions vertex/point labeling
- Set shouldLabelLines to true if user mentions side/edge labeling
- Recognize polygon names: triangle = 3, square = 4, pentagon = 5, hexagon = 6, etc.
- For rectangles: set numberOfSides = 4 and shapeType = "rectangle"
- For squares: set numberOfSides = 4 and shapeType = "square"
- For regular polygons: set shapeType = "regular" or omit

Color Detection:
- Extract fill colors from phrases like "red triangle", "blue square", "green pentagon"
- Extract stroke/border colors from phrases like "black border", "red outline", "blue edge"
- Support color names: red, blue, green, yellow, orange, purple, pink, brown, black, white, gray, etc.
- Support hex codes: #FF0000, #00FF00, #0000FF, etc.
- If only one color is mentioned, assume it's the fill color
- If "border", "outline", "edge", or "stroke" is mentioned with a color, set strokeColor
- Examples:
  - "red triangle" → fillColor: "red"
  - "blue square with black border" → fillColor: "blue", strokeColor: "black"
  - "green pentagon" → fillColor: "green"
  - "#FF0000 hexagon" → fillColor: "#FF0000"

- Do not include label names. That will be handled elsewhere.

Prompt: {input}
`);

export const shapeIntentChain = shapeIntentPrompt.pipe(
  new ChatOpenAI({
    model: "gpt-4o-mini",
    apiKey: import.meta.env.VITE_OPENAI_API_KEY,
    temperature: 0,
  }).withStructuredOutput(shapeIntentSchema)
);
