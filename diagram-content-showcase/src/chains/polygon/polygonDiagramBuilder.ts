// polygonDiagramBuilder.ts

import { PolygonDiagramParams } from "../../diagramTypes";
import { PolygonLabelOutput } from "./generatePolygonLabelsChain";
import { ShapeIntent } from "./shapeIntentChain";

// Color mapping for common color names to hex codes
const COLOR_MAP: Record<string, string> = {
  red: "#FF0000",
  blue: "#0000FF",
  green: "#008000",
  yellow: "#FFFF00",
  orange: "#FFA500",
  purple: "#800080",
  pink: "#FFC0CB",
  brown: "#A52A2A",
  black: "#000000",
  white: "#FFFFFF",
  gray: "#808080",
  grey: "#808080",
  cyan: "#00FFFF",
  magenta: "#FF00FF",
  lime: "#00FF00",
  navy: "#000080",
  maroon: "#800000",
  olive: "#808000",
  teal: "#008080",
  silver: "#C0C0C0",
  gold: "#FFD700",
  indigo: "#4B0082",
  violet: "#EE82EE",
  turquoise: "#40E0D0",
  coral: "#FF7F50",
  salmon: "#FA8072",
  khaki: "#F0E68C",
  plum: "#DDA0DD",
  orchid: "#DA70D6",
  tan: "#D2B48C",
  crimson: "#DC143C",
  azure: "#F0FFFF",
  beige: "#F5F5DC",
  bisque: "#FFE4C4",
  chocolate: "#D2691E",
  firebrick: "#B22222",
  forestgreen: "#228B22",
  fuchsia: "#FF00FF",
  goldenrod: "#DAA520",
  hotpink: "#FF69B4",
  lavender: "#E6E6FA",
  lemonchiffon: "#FFFACD",
  lightblue: "#ADD8E6",
  lightgreen: "#90EE90",
  lightyellow: "#FFFFE0",
  mediumblue: "#0000CD",
  mediumgreen: "#00FA9A",
  orangered: "#FF4500",
  palegreen: "#98FB98",
  royalblue: "#4169E1",
  skyblue: "#87CEEB",
  springgreen: "#00FF7F",
  steelblue: "#4682B4",
  tomato: "#FF6347",
  yellowgreen: "#9ACD32"
};

export function normalizeColor(color: string): string {
  if (!color) return "";

  const normalizedColor = color.toLowerCase().trim();

  // Check if it's a hex code (with or without #)
  if (/^#?[0-9a-f]{6}$/i.test(normalizedColor)) {
    return normalizedColor.startsWith('#') ? normalizedColor : `#${normalizedColor}`;
  }

  // Check if it's a 3-digit hex code
  if (/^#?[0-9a-f]{3}$/i.test(normalizedColor)) {
    const hex = normalizedColor.replace('#', '');
    return `#${hex[0]}${hex[0]}${hex[1]}${hex[1]}${hex[2]}${hex[2]}`;
  }

  // Check if it's a named color
  if (COLOR_MAP[normalizedColor]) {
    return COLOR_MAP[normalizedColor];
  }

  // Return empty string if color is not recognized
  return "";
}

export function generateRegularPolygon(
  sides: number,
  radius = 0.4,
  center = { x: 0.5, y: 0.5 }
): { x: number; y: number }[] {
  const angleStep = (2 * Math.PI) / sides;

  // Calculate starting angle for proper orientation
  let startAngle = 0;

  // For even-sided polygons (squares, hexagons, etc.), orient with flat bottom
  if (sides % 2 === 0) {
    startAngle = Math.PI / sides; // This ensures flat bottom edge
  } else {
    // For odd-sided polygons (triangles, pentagons), point upward
    startAngle = -Math.PI / 2;
  }

  return Array.from({ length: sides }, (_, i) => {
    const angle = i * angleStep + startAngle;
    return {
      x: parseFloat((center.x + radius * Math.cos(angle)).toFixed(4)),
      y: parseFloat((center.y + radius * Math.sin(angle)).toFixed(4)),
    };
  });
}

export function generateRectangle(
  width = 0.6,
  height = 0.4,
  center = { x: 0.5, y: 0.5 }
): { x: number; y: number }[] {
  const halfWidth = width / 2;
  const halfHeight = height / 2;

  return [
    { x: parseFloat((center.x - halfWidth).toFixed(4)), y: parseFloat((center.y - halfHeight).toFixed(4)) }, // Top-left
    { x: parseFloat((center.x + halfWidth).toFixed(4)), y: parseFloat((center.y - halfHeight).toFixed(4)) }, // Top-right
    { x: parseFloat((center.x + halfWidth).toFixed(4)), y: parseFloat((center.y + halfHeight).toFixed(4)) }, // Bottom-right
    { x: parseFloat((center.x - halfWidth).toFixed(4)), y: parseFloat((center.y + halfHeight).toFixed(4)) }, // Bottom-left
  ];
}

export function generateLineLabels(pointLabels: string[]): string[] {
  return pointLabels.map((label, i, arr) =>
    `${label}${arr[(i + 1) % arr.length]}`
  );
}

export async function buildPolygonDiagram(
  userPrompt: string,
  shape: ShapeIntent,
  labels?: PolygonLabelOutput
): Promise<PolygonDiagramParams> {
  const sides = Math.max(3, Math.min(20, shape.numberOfSides));

  // Generate points based on shape type
  let points: { x: number; y: number }[];

  if (shape.shapeType === "rectangle") {
    points = generateRectangle(0.6, 0.4); // Default rectangle proportions
  } else if (shape.shapeType === "square") {
    points = generateRectangle(0.5, 0.5); // Square proportions
  } else {
    points = generateRegularPolygon(sides);
  }

  // Apply colors from shape intent, with fallbacks to defaults
  const fillColor = shape.fillColor ? normalizeColor(shape.fillColor) : "#f5f5f5";
  const strokeColor = shape.strokeColor ? normalizeColor(shape.strokeColor) : "black";

  const diagram: PolygonDiagramParams = {
    diagramType: "polygon",
    width: 1,
    height: 1,
    points,
    fill: fillColor || "#f5f5f5", // Fallback if color normalization fails
    stroke: strokeColor || "black", // Fallback if color normalization fails
    strokeWidth: 1,
  };

  if (shape.shouldLabelPoints && labels?.pointLabels?.length === points.length) {
    diagram.pointLabels = labels.pointLabels;
  }

  if (shape.shouldLabelLines) {
  const numSides = points.length;

  if (labels?.lineLabels?.length === numSides) {
    diagram.lineLabels = labels.lineLabels;

  } else if (labels?.lineLabels && labels.lineLabels.length > 0) {
    const first = labels.lineLabels[0]; // ✅ now safe
    const allSame = labels.lineLabels.every(val => val === first);

    if (labels.lineLabels.length === 1 || allSame) {
      diagram.lineLabels = Array(numSides).fill(first);
    }

  } else if (diagram.pointLabels?.length === numSides) {
    diagram.lineLabels = generateLineLabels(diagram.pointLabels);
  }
}



  return diagram;
}
