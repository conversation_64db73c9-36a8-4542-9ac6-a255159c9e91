import { z } from "zod";
import { Chat<PERSON>penAI } from "@langchain/openai";
import { PromptTemplate } from "@langchain/core/prompts";
import { RunnableLambda } from "@langchain/core/runnables";

// 1. Schema
const numSetsSchema = z.object({
  numSets: z.number(),
});
type NumSetsOutput = z.infer<typeof numSetsSchema>;

// 2. LLM
const chat = new ChatOpenAI({
  model: "gpt-4o-mini",
  apiKey: import.meta.env.VITE_OPENAI_API_KEY,
  temperature: 0,
});

// 3. Prompt Template
const vennNumberExtractorPrompt = PromptTemplate.fromTemplate(
  `Analyze the following prompt and determine how many distinct sets are mentioned in the context of a Venn diagram.

Return only valid JSON in the format:
{{
  "numSets": number
}}

Rules:
- Count only unique sets or categories mentioned for inclusion in the diagram.
- Treat cases where a group is described that do not fit into any of the clear sets as an additional set
- Do NOT include any additional information or explanation.
- Do NOT guess beyond what is clearly implied in the prompt.
- Return only valid JSON — no extra text.

Prompt: {input}`
);

// 4. Chain
export const extractNumSetsChain = vennNumberExtractorPrompt
  .pipe(chat.withStructuredOutput(numSetsSchema))
  .pipe(
    new RunnableLambda<NumSetsOutput, NumSetsOutput>({
      func: async (output: NumSetsOutput) => {
        console.log("🔢 Extracted numSets:", output.numSets);
        return output;
      },
    })
  );
