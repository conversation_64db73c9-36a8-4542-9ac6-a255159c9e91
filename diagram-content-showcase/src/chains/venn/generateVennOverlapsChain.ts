import { z } from "zod";
import { ChatOpenAI } from "@langchain/openai";
import { PromptTemplate } from "@langchain/core/prompts";
import { RunnableLambda } from "@langchain/core/runnables";

// 1. Schema for overlap-only output
const vennOverlapsSchema = z.object({
  overlaps: z.array(
    z.object({
      sets: z.array(z.string()), // must be an intersection
      size: z.number(),
      label: z.string(),
    })
  ),
});

export type VennOverlapsOutput = z.infer<typeof vennOverlapsSchema>;

// 2. Chat model
const chat = new ChatOpenAI({
  model: "gpt-4o-mini",
  apiKey: import.meta.env.VITE_OPENAI_API_KEY,
  temperature: 0,
});

// 3. Prompt template
const vennOverlapsPrompt = PromptTemplate.fromTemplate(
  `You are generating overlaps (intersections) for a Venn diagram.

The user prompt was:
{initialPrompt}

The sets already defined in the diagram are:
{sets}

Your job is to find pairwise or multi-set intersections (if any are implied in the prompt) and return them as an array of overlap entries.

Return valid JSON in the format:
{{
  "overlaps": [
    {{"sets": [string, string], "size": number, "label": string}},
    ...
  ]
}}

Rules:
- If the prompt does not provide a number for the overlap, always set size to 3.
- Only include overlaps (2 or more sets in the "sets" array).
- Do NOT include individual sets.
- Do not invent overlaps. Only include overlaps if there is direct or strongly implied evidence in the prompt.
- You may include overlaps of 3 sets if clearly indicated in the prompt.
- Always use exact set names from the 'sets' list when filling the "sets" field.
- Labeling rules:
  - If the prompt contains specific counts (e.g., "15 people like both coffee and tea"), use the number as the label (e.g., "15").
  - Otherwise if counts are not available from the prompt, label using the names of overlapping sets joined with " and ", e.g., "coffee and tea".
- Return only valid JSON — no explanation, text, or markdown.`
);


// 4. Chain
export const generateVennOverlapsChain = vennOverlapsPrompt
  .pipe(chat.withStructuredOutput(vennOverlapsSchema))
  .pipe(
    new RunnableLambda<VennOverlapsOutput, VennOverlapsOutput>({
      func: async (output: VennOverlapsOutput) => {
        console.log("🔄 [Venn Overlaps Output]:", JSON.stringify(output, null, 2));
        return output;
      },
    })
  );
