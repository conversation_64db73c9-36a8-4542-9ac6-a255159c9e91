import { z } from "zod";
import { ChatOpenAI } from "@langchain/openai";
import { PromptTemplate } from "@langchain/core/prompts";
import { RunnableLambda } from "@langchain/core/runnables";

// 1. Schema for Venn output (unique individual sets only)
const vennUniqueSetsSchema = z.object({
  diagramType: z.literal("venn"),
  data: z.array(
    z.object({
      sets: z.array(z.string()), // single sets only
      size: z.number(),
      label: z.string(),
    })
  ),
});

export type VennUniqueSetsOutput = z.infer<typeof vennUniqueSetsSchema>;

// 2. Chat model
const chat = new ChatOpenAI({
  model: "gpt-4o-mini",
  apiKey: import.meta.env.VITE_OPENAI_API_KEY,
  temperature: 0,
});

const vennUniqueSetsPrompt = PromptTemplate.fromTemplate(
  `You are creating data for a Venn diagram that contains only individual sets — no overlaps or intersections.

The original user prompt was:
{initialPrompt}

There are {numSets} distinct sets.

Output valid JSON in this format:
{{
  "diagramType": "venn",
  "data": [
    {{"sets": [string], "size": number, "label": string}},
    ...
  ]
}}

Rules:
- Only include individual sets (e.g., ["Coffee"]) — do NOT include intersections like ["Coffee", "Tea"].
- If a group is mentioned that does not belong to any of the clear sets (e.g., "6 do not belong to any group", "4 people don't like any of..."), treat it as a separate individual set named "None".
  - Use "Out" in the "sets" array.
  - Use "Out" as the truncated identifier for labeling. For example: "Out(4)".
- Labeling:
  - If the prompt gives a number (e.g., "15 people like coffee", "6 do not belong to any"), truncate the set name to a maximum of 3 characters (e.g., "Cof" for "Coffee") and format the label as "Cof(15)".
  - If no number is given, use the full set name as the label.
  - Always return the label as a plain string — no markdown, no quotation marks inside the value.
- Always use the exact names from the prompt for the "sets" field — do not truncate or modify them (except "None" as specified).
- Return strict, valid JSON only — no trailing commas, no explanations, no markdown code blocks.`
);



// 3. Prompt template for generating unique (non-overlapping) Venn set regions


// 4. Chain: From prompt and numSets → unique Venn set regions
export const generateVennUniqueSetsChain = vennUniqueSetsPrompt
  .pipe(chat.withStructuredOutput(vennUniqueSetsSchema))
  .pipe(
    new RunnableLambda<VennUniqueSetsOutput, VennUniqueSetsOutput>({
      func: async (output: VennUniqueSetsOutput) => {
        console.log("🟢 [Venn Unique Sets Output]:", JSON.stringify(output, null, 2));
        return output;
      },
    })
  );
