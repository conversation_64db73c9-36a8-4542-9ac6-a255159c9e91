import { RunnableLambda } from "@langchain/core/runnables";
import { extractNumSetsChain } from "./extractNumSetsChain";
import { generateVennUniqueSetsChain } from "./generateVennUniqueSetsChain";
import { generateVennOverlapsChain } from "./generateVennOverlapsChain";

export const generateVennDiagramData = new RunnableLambda({
  func: async (promptData: { input: string }) => {
    const initialPrompt = promptData.input;
    const numSetsObject = await extractNumSetsChain.invoke(promptData);
    const uniqueSetsObject = await generateVennUniqueSetsChain.invoke({
      initialPrompt,
      numSets: numSetsObject.numSets,
    });
    uniqueSetsObject.data.forEach((entry) => (entry.size = 10));
    const overlapsObject = await generateVennOverlapsChain.invoke({
      sets: uniqueSetsObject,
      initialPrompt,
    });
    overlapsObject.overlaps.forEach((entry) => (entry.size = 3));
    return {
      ...uniqueSetsObject,
      data: [...uniqueSetsObject.data, ...overlapsObject.overlaps],
    };
  },
});