import { ChatOpenAI } from "@langchain/openai";
import { PromptTemplate } from "@langchain/core/prompts";
import { RunnableSequence } from "@langchain/core/runnables";
import { z } from "zod";
import { GraphDiagramParams, GraphSeries, DataPoint } from "../../diagramTypes";

// Initialize LLM
const chat = new ChatOpenAI({
  model: "gpt-4o-mini",
  apiKey: import.meta.env.VITE_OPENAI_API_KEY,
  temperature: 0,
});

// Zod schema for graph data validation
const DataPointSchema = z.object({
  x: z.number(),
  y: z.number(),
  label: z.string().optional(),
  color: z.string().optional(),
  size: z.number().optional(),
});

const GraphSeriesSchema = z.object({
  id: z.string(),
  name: z.string(),
  data: z.array(DataPointSchema),
  type: z.enum(['line', 'scatter', 'bar', 'area', 'spline', 'step']),
  color: z.string().optional(),
  strokeWidth: z.number().optional(),
  fillOpacity: z.number().optional(),
  visible: z.boolean().optional(),
  animated: z.boolean().optional(),
  animationDuration: z.number().optional(),
  animationDelay: z.number().optional(),
});

const GraphDiagramSchema = z.object({
  diagramType: z.literal("graph"),
  mode: z.enum(['plot', 'interactive']),
  width: z.number().optional(),
  height: z.number().optional(),
  series: z.array(GraphSeriesSchema),
  xAxis: z.object({
    label: z.string().optional(),
    min: z.number().optional(),
    max: z.number().optional(),
    tickCount: z.number().optional(),
    tickFormat: z.string().optional(),
    gridVisible: z.boolean().optional(),
    gridColor: z.string().optional(),
    gridOpacity: z.number().optional(),
    labelColor: z.string().optional(),
    labelFontSize: z.number().optional(),
  }).optional(),
  yAxis: z.object({
    label: z.string().optional(),
    min: z.number().optional(),
    max: z.number().optional(),
    tickCount: z.number().optional(),
    tickFormat: z.string().optional(),
    gridVisible: z.boolean().optional(),
    gridColor: z.string().optional(),
    gridOpacity: z.number().optional(),
    labelColor: z.string().optional(),
    labelFontSize: z.number().optional(),
  }).optional(),
  legend: z.object({
    visible: z.boolean().optional(),
    position: z.enum(['top', 'bottom', 'left', 'right', 'top-left', 'top-right', 'bottom-left', 'bottom-right']).optional(),
    orientation: z.enum(['horizontal', 'vertical']).optional(),
    backgroundColor: z.string().optional(),
    borderColor: z.string().optional(),
    textColor: z.string().optional(),
    fontSize: z.number().optional(),
  }).optional(),
  tooltip: z.object({
    enabled: z.boolean().optional(),
    format: z.string().optional(),
    backgroundColor: z.string().optional(),
    textColor: z.string().optional(),
    borderColor: z.string().optional(),
    fontSize: z.number().optional(),
  }).optional(),
  interaction: z.object({
    zoom: z.boolean().optional(),
    pan: z.boolean().optional(),
    select: z.boolean().optional(),
    brush: z.boolean().optional(),
    crosshair: z.boolean().optional(),
    hover: z.boolean().optional(),
  }).optional(),
  animation: z.object({
    enabled: z.boolean().optional(),
    duration: z.number().optional(),
    easing: z.enum(['linear', 'easeIn', 'easeOut', 'easeInOut', 'circIn', 'circOut', 'circInOut', 'backIn', 'backOut', 'backInOut']).optional(),
    stagger: z.number().optional(),
  }).optional(),
  backgroundColor: z.string().optional(),
  margin: z.object({
    top: z.number().optional(),
    right: z.number().optional(),
    bottom: z.number().optional(),
    left: z.number().optional(),
  }).optional(),
  theme: z.enum(['light', 'dark', 'custom']).optional(),
  customTheme: z.object({
    backgroundColor: z.string().optional(),
    textColor: z.string().optional(),
    gridColor: z.string().optional(),
    axisColor: z.string().optional(),
  }).optional(),
});

// Prompt template for graph generation
const graphPrompt = PromptTemplate.fromTemplate(`
You are an expert data visualization specialist. Based on the user's request, generate a comprehensive graph configuration.

User Request: {input}

Analyze the request and determine:
1. What type of graph(s) would best represent the data
2. Whether this should be in 'plot' mode (static) or 'interactive' mode (with controls)
3. What data series are needed
4. Appropriate styling and configuration

Generate a JSON object that matches this structure:
{{
  "diagramType": "graph",
  "mode": "plot" | "interactive",
  "width": number (optional, default 800),
  "height": number (optional, default 400),
  "series": [
    {{
      "id": "unique-id",
      "name": "Series Name",
      "data": [{{ "x": number, "y": number, "label": "optional", "color": "optional", "size": number }}],
      "type": "line" | "scatter" | "bar" | "area" | "spline" | "step",
      "color": "#hex-color",
      "strokeWidth": number,
      "fillOpacity": number (0-1),
      "visible": boolean,
      "animated": boolean,
      "animationDuration": number (ms),
      "animationDelay": number (ms)
    }}
  ],
  "xAxis": {{
    "label": "X Axis Label",
    "min": number,
    "max": number,
    "tickCount": number,
    "tickFormat": "format string",
    "gridVisible": boolean,
    "gridColor": "#hex-color",
    "gridOpacity": number (0-1),
    "labelColor": "#hex-color",
    "labelFontSize": number
  }},
  "yAxis": {{ /* same structure as xAxis */ }},
  "legend": {{
    "visible": boolean,
    "position": "top" | "bottom" | "left" | "right" | "top-left" | "top-right" | "bottom-left" | "bottom-right",
    "orientation": "horizontal" | "vertical",
    "backgroundColor": "#hex-color",
    "borderColor": "#hex-color",
    "textColor": "#hex-color",
    "fontSize": number
  }},
  "tooltip": {{
    "enabled": boolean,
    "format": "format string like '%{{x}}, %{{y}}'",
    "backgroundColor": "#hex-color",
    "textColor": "#hex-color",
    "borderColor": "#hex-color",
    "fontSize": number
  }},
  "interaction": {{
    "zoom": boolean,
    "pan": boolean,
    "select": boolean,
    "brush": boolean,
    "crosshair": boolean,
    "hover": boolean
  }},
  "animation": {{
    "enabled": boolean,
    "duration": number (ms),
    "easing": "linear" | "easeIn" | "easeOut" | "easeInOut" | "circIn" | "circOut" | "circInOut" | "backIn" | "backOut" | "backInOut",
    "stagger": number (ms between series animations)
  }},
  "backgroundColor": "#hex-color",
  "margin": {{ "top": number, "right": number, "bottom": number, "left": number }},
  "theme": "light" | "dark" | "custom",
  "customTheme": {{
    "backgroundColor": "#hex-color",
    "textColor": "#hex-color",
    "gridColor": "#hex-color",
    "axisColor": "#hex-color"
  }}
}}

Guidelines:
- For mathematical functions, generate appropriate data points
- For data visualization requests, create realistic sample data
- Use interactive mode for requests mentioning "interactive", "controls", "zoom", "pan", etc.
- Use plot mode for static displays or when specifically requested
- Choose appropriate colors that work well together
- Enable animations for engaging presentations
- Include proper axis labels and legends
- For multiple series, use different colors and consider overlaying them
- Generate enough data points for smooth curves (50-100 points for functions)
- Consider the context and choose appropriate graph types

Return only the JSON object, no additional text.
`);

// Chain to generate graph data
export const generateGraphDiagramData = RunnableSequence.from([
  graphPrompt,
  chat,
  (response) => {
    try {
      const jsonStr = response.content as string;
      const parsed = JSON.parse(jsonStr);
      const validated = GraphDiagramSchema.parse(parsed);
      return validated as GraphDiagramParams;
    } catch (error) {
      console.error("Error parsing graph data:", error);
      // Return a default graph configuration
      return {
        diagramType: "graph" as const,
        mode: "interactive" as const,
        series: [
          {
            id: "default",
            name: "Sample Data",
            data: [
              { x: 0, y: 0 },
              { x: 1, y: 1 },
              { x: 2, y: 4 },
              { x: 3, y: 9 },
              { x: 4, y: 16 },
            ],
            type: "line" as const,
            color: "#0077ff",
          },
        ],
        xAxis: { label: "X", gridVisible: true },
        yAxis: { label: "Y", gridVisible: true },
        legend: { visible: true, position: "top-right" as const },
        tooltip: { enabled: true },
        interaction: { zoom: true, pan: true, hover: true },
        animation: { enabled: true, duration: 1000, easing: "easeOut" as const },
      } as GraphDiagramParams;
    }
  },
]);
