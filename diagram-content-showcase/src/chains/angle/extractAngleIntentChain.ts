import { z } from "zod";
import { PromptTemplate } from "@langchain/core/prompts";
import { ChatOpenAI } from "@langchain/openai";

// 1. Schema
export const angleIntentSchema = z.object({
  toAngleDeg: z.number(),
  fromAngleDeg: z.number().optional(),         // defaults to 0 if not specified
  drawClockwise: z.boolean().optional(),
  draggable: z.enum(["from", "to", "both", "none"]).optional(),
  snapIncrement: z.number().optional(),
});

export type AngleIntent = z.infer<typeof angleIntentSchema>;

// 2. Prompt
const angleIntentPrompt = PromptTemplate.fromTemplate(`
You are extracting angle diagram intent from a user prompt.

Return only valid JSON with the following structure:
{{
  "toAngleDeg": number,
  "fromAngleDeg": number | null,         // optional, default to 0
  "drawClockwise": boolean | null,
  "draggable": "from" | "to" | "both" | "none" | null,
  "snapIncrement": number | null
}}

Guidelines:
- Default fromAngleDeg to 0 if not specified.
- If user says “clockwise” or “counterclockwise”, reflect that in drawClockwise, if unclear return false by default.
- If they mention dragging handles (e.g., "adjustable", "move ends"), set draggable accordingly.
- snapIncrement could be 15, 30, etc. if user says "snap to 15 degrees", etc.

Prompt: {prompt}
`);

// 3. Chain
export const extractAngleIntentChain = angleIntentPrompt.pipe(
  new ChatOpenAI({
    model: "gpt-4o-mini",
    temperature: 0,
    apiKey: import.meta.env.VITE_OPENAI_API_KEY,
  }).withStructuredOutput(angleIntentSchema)
);
