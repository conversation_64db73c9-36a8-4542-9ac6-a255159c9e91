import { RunnableLambda } from "@langchain/core/runnables";
import { extractAngleIntentChain } from "./extractAngleIntentChain";
import { extractAngleLabelsChain } from "./extractAngleLabelsChain";
import { extractAngleStyleChain } from "./extractAngleStyleChain";
import { AngleDiagramParams } from "../../diagramTypes";

export const generateAngleDiagramData = new RunnableLambda({
  func: async ({ input }: { input: string }): Promise<AngleDiagramParams> => {
    const prompt = input;

    // Step 1: Extract core intent
    const intent = await extractAngleIntentChain.invoke({ prompt });
    console.log("📐 Intent:", intent);

    // Step 2: Extract labels (angle name and points)
    const labelsResult = await extractAngleLabelsChain.invoke({ prompt });
    console.log("🏷️ Labels:", labelsResult);

    // Step 3: Extract styling preferences
    const styleResult = await extractAngleStyleChain.invoke({ prompt });
    console.log("🎨 Style:", styleResult);

    // Build only allowed fields
    const diagram: AngleDiagramParams = {
      diagramType: "angle",
      toAngleDeg: intent.toAngleDeg,
      fromAngleDeg: intent.fromAngleDeg,
      drawClockwise: intent.drawClockwise ?? undefined,
      draggable: intent.draggable ?? undefined,
      snapIncrement: intent.snapIncrement ?? undefined,
      labels: labelsResult.labels ?? undefined,
      angleLabel: labelsResult.angleLabel ?? undefined,
      arcColor: styleResult.arcColor ?? undefined,
      lineColor: styleResult.lineColor ?? undefined,
      labelColor: styleResult.labelColor ?? undefined,
      highlightColor: styleResult.highlightColor ?? undefined,
      fontSize: styleResult.fontSize ?? undefined,
      showHandles: intent.draggable !== "none",
    };

    console.log("✅ Final AngleDiagramParams:", diagram);
    return diagram;
  },
});
