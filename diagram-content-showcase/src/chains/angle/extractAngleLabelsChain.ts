import { z } from "zod";
import { PromptTemplate } from "@langchain/core/prompts";
import { ChatOpenAI } from "@langchain/openai";

// 1. Schema
export const angleLabelSchema = z.object({
  angleLabel: z.string().optional(),
  labels: z
    .object({
      from: z.string().optional(),
      vertex: z.string().optional(),
      to: z.string().optional(),
    })
    .optional(),
});

export type AngleLabelOutput = z.infer<typeof angleLabelSchema>;

// 2. Prompt
const angleLabelPrompt = PromptTemplate.fromTemplate(`
You are extracting labeling information for an angle diagram.

Return JSON:
{{
  "angleLabel"?: string,
  "labels"?: {{
    "from"?: string,
    "vertex"?: string,
    "to"?: string
  }}
}}

Guidelines:
- If the prompt includes an angle name like "∠ABC", "θ", or "30°", include it as angleLabel.
- If it names the three points that form the angle (like A, B, C), assign them to from, vertex, and to.
- Do not invent names — only extract them if clearly implied.
- If nothing is labeled, return an empty object.

Prompt: {prompt}
`);

// 3. Chain
export const extractAngleLabelsChain = angleLabelPrompt.pipe(
  new ChatOpenAI({
    model: "gpt-4o-mini",
    temperature: 0,
    apiKey: import.meta.env.VITE_OPENAI_API_KEY,
  }).withStructuredOutput(angleLabelSchema)
);
