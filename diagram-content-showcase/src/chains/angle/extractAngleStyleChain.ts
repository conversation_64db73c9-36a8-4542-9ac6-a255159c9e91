import { z } from "zod";
import { PromptTemplate } from "@langchain/core/prompts";
import { ChatOpenAI } from "@langchain/openai";

// 1. Schema
export const angleStyleSchema = z.object({
  arcColor: z.string().optional(),
  lineColor: z.string().optional(),
  labelColor: z.string().optional(),
  highlightColor: z.string().optional(),
  fontSize: z.number().optional(),
});

export type AngleStyleOutput = z.infer<typeof angleStyleSchema>;

// 2. Prompt
const angleStylePrompt = PromptTemplate.fromTemplate(`
You are extracting visual styling preferences for an angle diagram.

Return only valid JSON:
{{
  "arcColor"?: string,
  "lineColor"?: string,
  "labelColor"?: string,
  "highlightColor"?: string,
  "fontSize"?: number
}}

Guidelines:
- Colors may be specified by name ("red", "blue") or hex ("#ff0000").
- If the user says "use red for the arc", return arcColor: "red".
- If font size is mentioned ("use large text", "font size 16"), convert to a number.
- Do not return any values unless they are clearly specified.

Prompt: {prompt}
`);

// 3. Chain
export const extractAngleStyleChain = angleStylePrompt.pipe(
  new ChatOpenAI({
    model: "gpt-4o-mini",
    temperature: 0,
    apiKey: import.meta.env.VITE_OPENAI_API_KEY,
  }).withStructuredOutput(angleStyleSchema)
);
