# Diagram JSON Reference Guide

This comprehensive reference provides JSON schemas and examples for all diagram types and animation sequences in the diagram-content-showcase system.

## Table of Contents

1. [Overview](#overview)
2. [Basic Diagram Types](#basic-diagram-types)
   - [Venn Diagrams](#venn-diagrams)
   - [Polygon Diagrams](#polygon-diagrams)
   - [Angle Diagrams](#angle-diagrams)
   - [Graph Diagrams](#graph-diagrams)
3. [Animation & Transitions](#animation--transitions)
4. [Animated Sequences](#animated-sequences)
5. [Complete Examples](#complete-examples)

## Overview

The diagram system uses JSON definitions with a `diagramType` discriminator field to determine which component to render. Each diagram type has specific required and optional properties.

### Available Components

| Component | Purpose | Key Features |
|-----------|---------|--------------|
| `CompositeDiagram` | Single diagram rendering | Basic diagram display |
| `CompositeDiagramWithTransitions` | Single diagram with effects | Transitions + animations |
| `AnimatedDiagramSequence` | Basic sequence player | Timed diagram sequences |
| `AnimatedDiagramSequenceWithDynamicTransitions` | Advanced sequence | Dynamic transitions per diagram |
| `CompleteAnimatedDiagramSequence` | Full-featured player | Audio sync + playback controls |

### Diagram Types

| Diagram Type | `diagramType` Value | Purpose |
|--------------|-------------------|---------|
| Venn Diagrams | `"venn"` | Set relationships, overlaps, intersections |
| Polygon Diagrams | `"polygon"` | Geometric shapes, vertices, line segments |
| Angle Diagrams | `"angle"` | Angular measurements, interactive protractors |
| Graph Diagrams | `"graph"` | Mathematical functions, data visualization |

### Transition Types
Available transition effects: `"fade"`, `"zoom"`, `"slide"`, `"flip"`, `"drop"`, `"rotate"`, `"warp"`, `"diagonal"`, `"distortFade"`, `"ripple"`, `"glitch"`, `"explode"`, `"orbit"`

### Animation Types  
Available infinite animations: `"infiniteRotate"`, `"infiniteBounce"`, `"infinitePulse"`, `"infiniteWiggle"`, `"infiniteGlow"`

## Basic Diagram Types

### Venn Diagrams

Venn diagrams show set relationships, overlaps, and intersections between multiple sets.

#### Required Properties
- `diagramType`: Must be `"venn"`
- `data`: Array of set items defining regions

#### Set Item Properties
- `sets`: Array of set names (e.g., `["A"]` for single set, `["A", "B"]` for intersection)
- `size`: Number representing the size/cardinality of this region
- `label`: Optional display text for the region

#### Simple Two-Set Example
```json
{
  "diagramType": "venn",
  "data": [
    {
      "sets": ["Math"],
      "size": 15,
      "label": "Math Only (3)"
    },
    {
      "sets": ["Science"],
      "size": 12,
      "label": "Science Only (2)"
    },
    {
      "sets": ["Math", "Science"],
      "size": 8,
      "label": "5"
    }
  ]
}
```

#### Three-Set Example with Labels
```json
{
  "diagramType": "venn",
  "data": [
    { "sets": ["Programming"], "size": 20, "label": "Code" },
    { "sets": ["Math"], "size": 15, "label": "Numbers" },
    { "sets": ["Design"], "size": 12, "label": "Art" },
    { "sets": ["Programming", "Math"], "size": 8, "label": "Algorithms" },
    { "sets": ["Programming", "Design"], "size": 6, "label": "UI/UX" },
    { "sets": ["Math", "Design"], "size": 4, "label": "Data Viz" },
    { "sets": ["Programming", "Math", "Design"], "size": 3, "label": "Full Stack" }
  ]
}
```

### Polygon Diagrams

Polygon diagrams display geometric shapes with customizable vertices, styling, and labels.

#### Required Properties
- `diagramType`: Must be `"polygon"`
- `width`: Canvas width in pixels
- `height`: Canvas height in pixels  
- `points`: Array of vertex coordinates with `x` and `y` properties

#### Optional Properties
- `fill`: Fill color (default: `"transparent"`)
- `stroke`: Stroke color (default: `"#000000"`)
- `strokeWidth`: Stroke width in pixels (default: `2`)
- `pointLabels`: Array of vertex labels
- `lineLabels`: Array of edge labels

#### Simple Triangle
```json
{
  "diagramType": "polygon",
  "width": 400,
  "height": 300,
  "points": [
    { "x": 200, "y": 50 },
    { "x": 100, "y": 250 },
    { "x": 300, "y": 250 }
  ],
  "fill": "#e3f2fd",
  "stroke": "#1976d2",
  "strokeWidth": 2
}
```

#### Labeled Pentagon
```json
{
  "diagramType": "polygon",
  "width": 400,
  "height": 300,
  "points": [
    { "x": 200, "y": 50 },
    { "x": 350, "y": 150 },
    { "x": 300, "y": 250 },
    { "x": 100, "y": 250 },
    { "x": 50, "y": 150 }
  ],
  "fill": "#e3f2fd",
  "stroke": "#1976d2",
  "strokeWidth": 3,
  "pointLabels": ["A", "B", "C", "D", "E"],
  "lineLabels": ["AB", "BC", "CD", "DE", "EA"]
}
```

### Angle Diagrams

Angle diagrams display angular measurements with interactive controls and customizable styling.

#### Required Properties
- `diagramType`: Must be `"angle"`
- `toAngleDeg`: End angle in degrees (0-360)

#### Optional Properties
- `fromAngleDeg`: Start angle in degrees (default: `0`)
- `draggable`: Interaction mode - `"from"`, `"to"`, `"both"`, or `"none"` (default: `"none"`)
- `snapIncrement`: Snap angle increment (default: `1`)
- `drawClockwise`: Direction of measurement (default: `true`)
- `arcColor`: Arc color (default: `"#2196f3"`)
- `lineColor`: Line color (default: `"#424242"`)
- `labelColor`: Label color (default: `"#212121"`)
- `fontSize`: Font size (default: `14`)
- `angleLabel`: Angle label text
- `highlightColor`: Highlight color (default: `"#ff5722"`)
- `showHandles`: Show drag handles (default: `true`)
- `labels`: Point labels with `_from`, `_vertex`, `_to` properties

#### Simple 45° Angle
```json
{
  "diagramType": "angle",
  "fromAngleDeg": 0,
  "toAngleDeg": 45,
  "arcColor": "#2196f3",
  "angleLabel": "45°"
}
```

#### Interactive Angle with Labels
```json
{
  "diagramType": "angle",
  "fromAngleDeg": 0,
  "toAngleDeg": 60,
  "draggable": "both",
  "snapIncrement": 15,
  "arcColor": "#4caf50",
  "lineColor": "#2e7d32",
  "labelColor": "#1b5e20",
  "fontSize": 18,
  "labels": {
    "_from": "Ray 1",
    "_vertex": "Vertex",
    "_to": "Ray 2"
  },
  "angleLabel": "∠ABC",
  "highlightColor": "#ff9800",
  "showHandles": true
}
```

### Graph Diagrams

Graph diagrams provide mathematical function plotting and data visualization capabilities. You can use either **equation-based** (preferred for mathematical functions) or **series-based** (for pre-calculated data) approaches.

#### Required Properties
- `diagramType`: Must be `"graph"`
- `mode`: Either `"plot"` (static) or `"interactive"` (with controls)

#### Equation-Based Approach (Preferred for Math Functions)
- `equation`: Mathematical equation string (e.g., `"sin(x)"`, `"x^2"`, `"cos(t), sin(t)"`)
- `graphType`: Type of equation - `"function"`, `"parametric"`, `"polar"`, or `"numberline"`

Optional equation properties:
- `domain`: X-axis range for evaluation (default: `{min: -10, max: 10}`)
- `resolution`: Number of points to generate (default: `200`)

#### Series-Based Approach (For Pre-calculated Data)
- `series`: Array of data series with pre-calculated points

Each series requires:
- `id`: Unique identifier
- `name`: Display name for legend
- `data`: Array of data points with `x`, `y` coordinates
- `type`: Chart type - `"line"`, `"scatter"`, `"bar"`, `"area"`, `"spline"`, or `"step"`

#### Common Optional Properties
- `width`, `height`: Canvas dimensions
- `xAxis`, `yAxis`: Axis configuration with `label`, `min`, `max`, `gridVisible`
- `legend`: Legend settings with `visible`, `position`
- `interaction`: Enable `zoom`, `pan`, `hover`
- `backgroundColor`: Background color
- `theme`: `"light"`, `"dark"`, or `"custom"`

#### Simple Function Graph (Equation-Based)
```json
{
  "diagramType": "graph",
  "mode": "plot",
  "equation": "sin(x)",
  "graphType": "function",
  "domain": {"min": 0, "max": 6.28},
  "xAxis": {"label": "Radians"},
  "yAxis": {"label": "Amplitude"}
}
```

#### Parametric Graph (Heart Shape)
```json
{
  "diagramType": "graph",
  "mode": "plot",
  "equation": "16*sin(t)^3, 13*cos(t) - 5*cos(2*t) - 2*cos(3*t) - cos(4*t)",
  "graphType": "parametric",
  "domain": {"min": 0, "max": 6.28},
  "resolution": 300
}
```

#### Polar Graph (Rose Curve)
```json
{
  "diagramType": "graph",
  "mode": "plot",
  "equation": "cos(3*theta)",
  "graphType": "polar",
  "domain": {"min": 0, "max": 6.28}
}
```

#### Multiple Mathematical Functions
```json
{
  "diagramType": "graph",
  "mode": "interactive",
  "equation": "sin(x)",
  "graphType": "function",
  "domain": {"min": -6.28, "max": 6.28},
  "xAxis": {"label": "X", "gridVisible": true},
  "yAxis": {"label": "Y", "gridVisible": true},
  "legend": {"visible": true, "position": "top-right"},
  "interaction": {"zoom": true, "pan": true, "hover": true}
}
```

#### Pre-calculated Data Series
```json
{
  "diagramType": "graph",
  "mode": "plot",
  "series": [
    {
      "id": "data",
      "name": "Sample Data",
      "data": [
        {"x": 1, "y": 100},
        {"x": 2, "y": 150},
        {"x": 3, "y": 120},
        {"x": 4, "y": 200}
      ],
      "type": "bar",
      "color": "#4caf50"
    }
  ],
  "xAxis": {"label": "Time"},
  "yAxis": {"label": "Value"}
}
```

## Animation & Transitions

### Single Diagram with Effects

Use `CompositeDiagramWithTransitions` to add transition and animation effects to any diagram:

```json
{
  "diagramType": "venn",
  "data": [
    {"sets": ["A"], "size": 10},
    {"sets": ["B"], "size": 8},
    {"sets": ["A", "B"], "size": 3}
  ]
}
```

**Component Usage:**
```jsx
<CompositeDiagramWithTransitions
  definition={diagramDefinition}
  transitionType="fade"
  animationType="infinitePulse"
/>
```

### Available Transition Effects
- `"fade"`: Opacity transition
- `"zoom"`: Scale with blur effect
- `"slide"`: Horizontal slide movement
- `"flip"`: 3D flip rotation
- `"drop"`: Drop from above with spring
- `"rotate"`: Rotation with scale
- `"warp"`: Scale with heavy blur
- `"diagonal"`: Diagonal slide
- `"distortFade"`: Distorted fade
- `"ripple"`: Ripple effect
- `"glitch"`: Glitch effect
- `"explode"`: Explosive entrance
- `"orbit"`: Orbital motion

### Available Animation Effects
- `"infiniteRotate"`: Continuous rotation
- `"infiniteBounce"`: Vertical bouncing
- `"infinitePulse"`: Scale pulsing
- `"infiniteWiggle"`: Rotation wiggle
- `"infiniteGlow"`: Glow effect

## Animated Sequences

### Basic Animated Sequence

Use `AnimatedDiagramSequence` for simple timed sequences. Each diagram includes a `duration` property:

```json
[
  {
    "diagramType": "venn",
    "data": [{"sets": ["A"], "size": 10}],
    "duration": 3000
  },
  {
    "diagramType": "polygon",
    "width": 400,
    "height": 300,
    "points": [
      {"x": 200, "y": 50},
      {"x": 100, "y": 250},
      {"x": 300, "y": 250}
    ],
    "duration": 2000
  }
]
```

**Component Usage:**
```jsx
<AnimatedDiagramSequence sequence={sequenceData} loop={true} />
```

### Sequence with Dynamic Transitions

Use `AnimatedDiagramSequenceWithDynamicTransitions` for sequences where each diagram can have its own transition effect:

```json
[
  {
    "diagramType": "venn",
    "data": [{"sets": ["A"], "size": 10}],
    "duration": 3000,
    "transitionType": "fade"
  },
  {
    "diagramType": "angle",
    "fromAngleDeg": 0,
    "toAngleDeg": 90,
    "duration": 2000,
    "transitionType": "zoom"
  },
  {
    "diagramType": "graph",
    "mode": "plot",
    "equation": "x^2",
    "graphType": "function",
    "domain": {"min": -3, "max": 3},
    "duration": 4000,
    "transitionType": "slide"
  }
]
```

**Component Usage:**
```jsx
<AnimatedDiagramSequenceWithDynamicTransitions
  sequence={sequenceData}
  loop={false}
/>
```

### Complete Sequence with Audio Controls

Use `CompleteAnimatedDiagramSequence` for full-featured presentations with audio synchronization and playback controls:

```json
[
  {
    "diagramType": "venn",
    "data": [
      {"sets": ["Math"], "size": 15, "label": "Math Students"},
      {"sets": ["Science"], "size": 12, "label": "Science Students"}
    ],
    "duration": 5000,
    "audioUrl": "/audio/intro.mp3",
    "transitionType": "fade",
    "animationType": "infinitePulse"
  },
  {
    "diagramType": "venn",
    "data": [
      {"sets": ["Math"], "size": 15},
      {"sets": ["Science"], "size": 12},
      {"sets": ["Math", "Science"], "size": 8, "label": "Both"}
    ],
    "duration": 4000,
    "audioUrl": "/audio/overlap.mp3",
    "transitionType": "zoom"
  }
]
```

**Component Usage:**
```jsx
<CompleteAnimatedDiagramSequence
  sequence={sequenceData}
  loop={true}
  playOnRender={true}
/>
```

**Features:**
- Play/pause controls
- Audio synchronization
- Progress indicator
- Loop functionality
- Auto-play option

## Complete Examples

### Educational Sequence Example

A complete educational sequence showing mathematical concepts:

```json
[
  {
    "diagramType": "venn",
    "data": [
      {"sets": ["Even"], "size": 20, "label": "Even Numbers"},
      {"sets": ["Prime"], "size": 15, "label": "Prime Numbers"}
    ],
    "duration": 4000,
    "transitionType": "fade",
    "audioUrl": "/audio/intro-sets.mp3"
  },
  {
    "diagramType": "venn",
    "data": [
      {"sets": ["Even"], "size": 20},
      {"sets": ["Prime"], "size": 15},
      {"sets": ["Even", "Prime"], "size": 1, "label": "Only 2"}
    ],
    "duration": 3000,
    "transitionType": "zoom",
    "audioUrl": "/audio/intersection.mp3"
  },
  {
    "diagramType": "graph",
    "mode": "plot",
    "equation": "sin(x)",
    "graphType": "function",
    "domain": {"min": 0, "max": 6.28},
    "duration": 5000,
    "transitionType": "slide",
    "audioUrl": "/audio/sine-wave.mp3"
  }
]
```

### Interactive Dashboard Example

Complex multi-series graph with full interactivity:

```json
{
  "diagramType": "graph",
  "mode": "interactive",
  "width": 900,
  "height": 600,
  "series": [
    {
      "id": "sales",
      "name": "Sales Data",
      "data": [
        {"x": 1, "y": 100}, {"x": 2, "y": 150}, {"x": 3, "y": 120},
        {"x": 4, "y": 200}, {"x": 5, "y": 180}, {"x": 6, "y": 250}
      ],
      "type": "line",
      "color": "#2196f3",
      "strokeWidth": 3,
      "animated": true,
      "animationDuration": 2000
    },
    {
      "id": "targets",
      "name": "Targets",
      "data": [
        {"x": 1, "y": 120}, {"x": 2, "y": 140}, {"x": 3, "y": 160},
        {"x": 4, "y": 180}, {"x": 5, "y": 200}, {"x": 6, "y": 220}
      ],
      "type": "line",
      "color": "#ff9800",
      "strokeWidth": 2
    }
  ],
  "xAxis": {
    "label": "Month",
    "gridVisible": true,
    "gridColor": "#f0f0f0"
  },
  "yAxis": {
    "label": "Revenue ($K)",
    "gridVisible": true,
    "gridColor": "#f0f0f0"
  },
  "legend": {
    "visible": true,
    "position": "top-right",
    "backgroundColor": "rgba(255,255,255,0.9)"
  },
  "interaction": {
    "zoom": true,
    "pan": true,
    "hover": true,
    "crosshair": true
  },
  "theme": "light"
}
```

### Mathematical Function Showcase

```json
{
  "diagramType": "graph",
  "mode": "interactive",
  "equation": "sin(x) + 0.5*sin(3*x)",
  "graphType": "function",
  "domain": {"min": -6.28, "max": 6.28},
  "resolution": 400,
  "xAxis": {
    "label": "X (radians)",
    "gridVisible": true,
    "gridColor": "#e0e0e0"
  },
  "yAxis": {
    "label": "Y",
    "gridVisible": true,
    "gridColor": "#e0e0e0"
  },
  "legend": {
    "visible": true,
    "position": "top-left"
  },
  "interaction": {
    "zoom": true,
    "pan": true,
    "hover": true,
    "crosshair": true
  },
  "theme": "light",
  "backgroundColor": "#fafafa"
}
```

## Best Practices

### JSON Structure
- Always include the `diagramType` field as the first property
- Use consistent property ordering for readability
- Include meaningful `id` and `name` values for series
- Provide descriptive labels for axes and legends
- Use the `label` property in VennSetItem for clear region identification

### Graph Equations
- **Prefer equation-based approach** for mathematical functions
- Use `"function"` graphType for y = f(x) equations like `"sin(x)"`, `"x^2"`
- Use `"parametric"` graphType for x(t), y(t) equations like `"cos(t), sin(t)"`
- Use `"polar"` graphType for r = f(θ) equations like `"1 + cos(theta)"`
- Set appropriate `domain` and `resolution` for smooth curves

### Performance
- Limit data points to < 1000 for smooth animations
- Use `"plot"` mode for static displays
- Choose appropriate chart types (`line` vs `spline` vs `scatter`)
- Enable animations selectively for better UX

### Sequences
- Keep individual diagram durations between 2-6 seconds
- Use transition types that match content flow
- Sync audio duration with diagram duration
- Test loop functionality for educational content

### Accessibility
- Use high contrast colors
- Provide meaningful labels and descriptions
- Include audio narration for complex sequences
- Test with screen readers

This JSON reference guide provides everything needed to create rich, interactive diagram presentations with equation-based mathematical functions in the diagram-content-showcase system.
