{
    "title": "Introduction to Python Programming",
    "description": "A comprehensive course covering Python fundamentals for beginners.",
    "thumbnail": "https://example.com/python-course.jpg",
    "topics": [
        {
            "title": "Python Basics",
            "description": "Learn the fundamental concepts of Python programming.",
            "other_details": {
                "difficulty": "beginner",
                "prerequisites": "none"
            },
            "order": 1,
            "sub_topics": [
                {
                    "title": "Variables and Data Types",
                    "description": "Understanding Python variables and basic data types",
                    "order": 1,
                    "lessons": [
                        {
                            "title": "Introduction to Variables",
                            "order": 1,
                            "sections": [
                                {
                                    "id": "hash",
                                    "type": "text",
                                    "url": "",
                                    "text": "**Author**: <PERSON>\n\n**Content**:\n\nVariables are named locations used to store data in a program. \n\n### Key Concepts:\n- Variable names should be descriptive\n- Python is dynamically typed\n- Use `=` for assignment\n\n```python\n# Example\nage = 25\nname = \"Alice\"\n```",
                                    "json_data": "",
                                    "order": 1,
                                    "duration": 30
                                },
                                {
                                    "id": "hash",
                                    "type": "video",
                                    "text": "**Some text description**",
                                    "url": "https://example.com/python-variables-video",
                                    "json_data": null,
                                    "order": 2,
                                    "duration": 30
                                }
                            ]
                        }
                    ],
                    "quiz": {
                        "title": "**Title goes here**",
                        "duration": 30,
                        "difficulty_level": "easy", // easy | medium | hard | advance
                        "instruction": "Answer each question carefully",
                        "questions": [
                            {
                                "sections": [
                                    {
                                        "id": "hash",
                                        "type": "text",
                                        "text": "**some text**",
                                        "url": "",
                                        "json_data": null,
                                        "order": 1
                                    },
                                    {
                                        "id": "hash",
                                        "type": "video",
                                        "text": "**some description**",
                                        "url": "https://example.com/python-variables-video",
                                        "json_data": null,
                                        "order": 2
                                    },
                                    {
                                        "id": "hash",
                                        "type": "animation-sequence",
                                        "text": "**some description**",
                                        "url": "",
                                        "json_data": [], // (could be an array or json, so just make it any type)
                                        "order": 3
                                    }
                                ],
                                "options": [
                                    {
                                        "id": "hash",
                                        "type": "text",
                                        "text": "**some text**",
                                        "url": "",
                                        "json_data": null,
                                        "order": 1
                                    },
                                    {
                                        "id": "hash",
                                        "type": "audio",
                                        "text": "**some description**",
                                        "url": "https://example.com/python-variables-video",
                                        "json_data": null,
                                        "order": 2
                                    },
                                    {
                                        "id": "hash",
                                        "type": "animation-sequence",
                                        "text": "**some description**",
                                        "url": "",
                                        "json_data": [], // (could be an array or json, so just make it any type)
                                        "order": 3
                                    }
                                ],
                                "answer": "",
                                "answer_explanation": "",
                                "order": 1,
                                "marks": 10
                            },
                            {
                                "sections": [
                                    {
                                        "id": "hash",
                                        "type": "text",
                                        "text": "**some text**",
                                        "url": "",
                                        "json_data": null,
                                        "order": 1
                                    },
                                    {
                                        "id": "hash",
                                        "type": "video",
                                        "text": "**some description**",
                                        "url": "https://example.com/python-variables-video",
                                        "json_data": null,
                                        "order": 2
                                    },
                                    {
                                        "id": "hash",
                                        "type": "animation-sequence",
                                        "text": "**some description**",
                                        "url": "",
                                        "json_data": [], // (could be an array or json, so just make it any type)
                                        "order": 3
                                    }
                                ],
                                "options": [],
                                "answer": "",
                                "answer_explanation": "",
                                "order": 2,
                                "marks": 15
                            }
                        ]
                    }
                }
            ]
        }
    ]
}