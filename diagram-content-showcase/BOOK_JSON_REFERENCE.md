# Book.json Construction Guide

This comprehensive guide documents how to construct Book.json files for the educational content system, including the content hashing system for deduplication.

## Table of Contents

1. [Overview](#overview)
2. [Book Structure](#book-structure)
3. [Content Section Types](#content-section-types)
4. [Hashing System](#hashing-system)
5. [Diagram Integration](#diagram-integration)
6. [Complete Examples](#complete-examples)
7. [Best Practices](#best-practices)

## Overview

The Book.json format defines educational content with a hierarchical structure: **Book → Topics → Sub-topics → Lessons → Sections**. Each section can contain different types of content (text, media, diagrams) and uses a hashing system to prevent duplication in the database.

### Key Features
- **Hierarchical content organization**
- **Multiple content types** (text, video, audio, image, diagrams)
- **Automatic deduplication** through content hashing
- **Rich diagram support** with all animation sequence types
- **Quiz system** with multimedia questions and options

## Book Structure

### Root Level Properties
```json
{
  "title": "Course Title",
  "description": "Course description",
  "thumbnail": "https://example.com/thumbnail.jpg",
  "topics": []
}
```

### Topic Structure
```json
{
  "title": "Topic Title",
  "description": "Topic description",
  "other_details": {
    "difficulty": "beginner|intermediate|advanced",
    "prerequisites": "Prerequisites description"
  },
  "order": 1,
  "sub_topics": []
}
```

### Sub-topic Structure
```json
{
  "title": "Sub-topic Title",
  "description": "Sub-topic description",
  "order": 1,
  "lessons": [],
  "quiz": {
    "title": "Quiz Title",
    "duration": 30,
    "difficulty_level": "easy|medium|hard|advance",
    "instruction": "Quiz instructions",
    "questions": []
  }
}
```

### Lesson Structure
```json
{
  "title": "Lesson Title",
  "order": 1,
  "sections": []
}
```

## Content Section Types

All content sections follow this base structure with type-specific variations:

### Base Section Structure
```json
{
  "id": "hash",                    // Auto-generated hash
  "type": "content_type",          // See types below
  "text": "Description text",       // Always present (can be empty)
  "url": "media_url",             // For media types (can be empty)
  "json_data": {},                // For diagram types (can be null)
  "order": 1,                     // Display order
  "duration": 30                  // Optional: content duration in seconds
}
```

### 1. Text Content
```json
{
  "id": "hash",
  "type": "text",
  "text": "**Author**: John Doe\n\n**Content**:\n\nVariables are named locations...",
  "url": "",
  "json_data": null,
  "order": 1,
  "duration": 30
}
```

### 2. Video Content
```json
{
  "id": "hash",
  "type": "video",
  "text": "Video description or transcript",
  "url": "https://example.com/video.mp4",
  "json_data": null,
  "order": 2,
  "duration": 120
}
```

### 3. Audio Content
```json
{
  "id": "hash",
  "type": "audio",
  "text": "Audio description or transcript",
  "url": "https://example.com/audio.mp3",
  "json_data": null,
  "order": 3,
  "duration": 60
}
```

### 4. Image Content
```json
{
  "id": "hash",
  "type": "image",
  "text": "Image caption or description",
  "url": "https://example.com/image.jpg",
  "json_data": null,
  "order": 4
}
```

### 5. Diagram Content Types

#### Simple Diagram
```json
{
  "id": "hash",
  "type": "diagram",
  "text": "Venn diagram showing set relationships",
  "url": "",
  "json_data": {
    "diagramType": "venn",
    "data": [
      {"sets": ["Math"], "size": 15, "label": "Math Only"},
      {"sets": ["Science"], "size": 12, "label": "Science Only"},
      {"sets": ["Math", "Science"], "size": 8, "label": "Both"}
    ]
  },
  "order": 5
}
```

#### Diagram with Transitions
```json
{
  "id": "hash",
  "type": "diagram-with-transitions",
  "text": "Interactive angle measurement with animations",
  "url": "",
  "json_data": {
    "definition": {
      "diagramType": "angle",
      "fromAngleDeg": 0,
      "toAngleDeg": 60,
      "draggable": "both"
    },
    "transitionType": "fade",
    "animationType": "infinitePulse"
  },
  "order": 6
}
```

#### Animation Sequence
```json
{
  "id": "hash",
  "type": "animation-sequence",
  "text": "Animated sequence showing mathematical progression",
  "url": "",
  "json_data": [
    {
      "diagramType": "venn",
      "data": [{"sets": ["A"], "size": 10}],
      "duration": 3000,
      "transitionType": "fade"
    },
    {
      "diagramType": "graph",
      "mode": "plot",
      "equation": "sin(x)",
      "graphType": "function",
      "duration": 4000,
      "transitionType": "slide"
    }
  ],
  "order": 7
}
```

#### Complete Animation Sequence with Audio
```json
{
  "id": "hash",
  "type": "complete-animation-sequence",
  "text": "Full educational sequence with narration",
  "url": "",
  "json_data": {
    "sequence": [
      {
        "diagramType": "venn",
        "data": [{"sets": ["Math"], "size": 15, "label": "Math"}],
        "duration": 5000,
        "audioUrl": "/audio/intro.mp3",
        "transitionType": "fade"
      }
    ],
    "loop": false,
    "playOnRender": true
  },
  "order": 8
}
```

## Hashing System

### Hash Generation Rules

The system generates unique IDs by hashing only the **content-defining properties**:

#### Hashable Properties (Order Matters)
1. `type` - Content type identifier
2. `url` - Media URL (if applicable)
3. `text` - Text content
4. `json_data` - Diagram/interactive content data

#### Non-Hashable Properties
- `id` - Generated from hash
- `order` - Display sequence
- `duration` - Timing information

### Hash Algorithm
```javascript
// Pseudo-code for hash generation
function generateSectionId(section) {
  const hashableContent = {
    type: section.type,
    url: section.url || "",
    text: section.text || "",
    json_data: section.json_data
  };
  
  // Sort keys for consistent hashing
  const sortedContent = sortObjectKeys(hashableContent);
  const contentString = JSON.stringify(sortedContent);
  
  return sha256(contentString);
}
```

### Deduplication Benefits
- **Storage efficiency**: Identical content stored once
- **Consistency**: Same content appears identically across courses
- **Maintenance**: Update content once, reflects everywhere
- **Analytics**: Track content performance across contexts

## Quiz System

### Quiz Structure
```json
{
  "title": "Quiz Title",
  "duration": 30,
  "difficulty_level": "easy|medium|hard|advance",
  "instruction": "Answer each question carefully",
  "questions": []
}
```

### Question Structure
```json
{
  "sections": [],           // Question content (same section types as lessons)
  "options": [],           // Answer options (same section types as lessons)
  "answer": "correct_option_id",
  "answer_explanation": "Explanation text",
  "order": 1,
  "marks": 10
}
```

### Quiz Question Example
```json
{
  "sections": [
    {
      "id": "hash",
      "type": "text",
      "text": "**Question**: What is a Python variable?",
      "url": "",
      "json_data": null,
      "order": 1
    },
    {
      "id": "hash",
      "type": "diagram",
      "text": "Variable assignment visualization",
      "url": "",
      "json_data": {
        "diagramType": "graph",
        "mode": "plot",
        "equation": "x",
        "graphType": "function",
        "domain": {"min": 0, "max": 10}
      },
      "order": 2
    }
  ],
  "options": [
    {
      "id": "option_a_hash",
      "type": "text",
      "text": "A) A named location in memory",
      "url": "",
      "json_data": null,
      "order": 1
    },
    {
      "id": "option_b_hash",
      "type": "text",
      "text": "B) A type of function",
      "url": "",
      "json_data": null,
      "order": 2
    }
  ],
  "answer": "option_a_hash",
  "answer_explanation": "Variables are named locations in memory that store data values.",
  "order": 1,
  "marks": 10
}
```

## Diagram Integration

### All Supported Diagram Types

#### 1. Basic Diagrams
```json
// Venn Diagram
{
  "type": "diagram",
  "json_data": {
    "diagramType": "venn",
    "data": [
      {"sets": ["A"], "size": 10, "label": "Set A"},
      {"sets": ["B"], "size": 8, "label": "Set B"},
      {"sets": ["A", "B"], "size": 3, "label": "Intersection"}
    ]
  }
}

// Polygon Diagram
{
  "type": "diagram",
  "json_data": {
    "diagramType": "polygon",
    "width": 400,
    "height": 300,
    "points": [
      {"x": 200, "y": 50},
      {"x": 100, "y": 250},
      {"x": 300, "y": 250}
    ],
    "fill": "#e3f2fd",
    "pointLabels": ["A", "B", "C"]
  }
}

// Angle Diagram
{
  "type": "diagram",
  "json_data": {
    "diagramType": "angle",
    "fromAngleDeg": 0,
    "toAngleDeg": 45,
    "draggable": "both",
    "angleLabel": "45°"
  }
}

// Graph Diagram (Equation-based)
{
  "type": "diagram",
  "json_data": {
    "diagramType": "graph",
    "mode": "interactive",
    "equation": "sin(x)",
    "graphType": "function",
    "domain": {"min": 0, "max": 6.28}
  }
}
```

#### 2. Enhanced Diagrams with Transitions
```json
{
  "type": "diagram-with-transitions",
  "json_data": {
    "definition": {
      "diagramType": "graph",
      "equation": "x^2",
      "graphType": "function"
    },
    "transitionType": "zoom",
    "animationType": "infiniteRotate"
  }
}
```

#### 3. Animation Sequences
```json
// Basic Animation Sequence
{
  "type": "animation-sequence",
  "json_data": [
    {
      "diagramType": "venn",
      "data": [{"sets": ["A"], "size": 10}],
      "duration": 3000
    },
    {
      "diagramType": "polygon",
      "width": 300,
      "height": 300,
      "points": [{"x": 150, "y": 50}, {"x": 50, "y": 250}, {"x": 250, "y": 250}],
      "duration": 2000
    }
  ]
}

// Dynamic Transitions Sequence
{
  "type": "animation-sequence-dynamic",
  "json_data": [
    {
      "diagramType": "angle",
      "toAngleDeg": 90,
      "duration": 2000,
      "transitionType": "fade"
    },
    {
      "diagramType": "graph",
      "equation": "cos(x)",
      "graphType": "function",
      "duration": 3000,
      "transitionType": "slide"
    }
  ]
}

// Complete Animation Sequence with Audio
{
  "type": "complete-animation-sequence",
  "json_data": {
    "sequence": [
      {
        "diagramType": "venn",
        "data": [{"sets": ["Math"], "size": 15, "label": "Mathematics"}],
        "duration": 5000,
        "audioUrl": "/audio/math-intro.mp3",
        "transitionType": "fade",
        "animationType": "infinitePulse"
      }
    ],
    "loop": false,
    "playOnRender": true
  }
}
```

## Complete Examples

### Example 1: Mathematics Lesson with Mixed Content

```json
{
  "title": "Introduction to Trigonometry",
  "order": 1,
  "sections": [
    {
      "id": "hash",
      "type": "text",
      "text": "**Introduction to Sine Function**\n\nThe sine function is one of the fundamental trigonometric functions...",
      "url": "",
      "json_data": null,
      "order": 1,
      "duration": 60
    },
    {
      "id": "hash",
      "type": "video",
      "text": "Visual explanation of sine wave formation",
      "url": "https://example.com/sine-wave-video.mp4",
      "json_data": null,
      "order": 2,
      "duration": 180
    },
    {
      "id": "hash",
      "type": "diagram",
      "text": "Interactive sine function graph",
      "url": "",
      "json_data": {
        "diagramType": "graph",
        "mode": "interactive",
        "equation": "sin(x)",
        "graphType": "function",
        "domain": {"min": 0, "max": 6.28},
        "xAxis": {"label": "Angle (radians)", "gridVisible": true},
        "yAxis": {"label": "Sine value", "gridVisible": true},
        "interaction": {"zoom": true, "pan": true}
      },
      "order": 3,
      "duration": 120
    },
    {
      "id": "hash",
      "type": "animation-sequence",
      "text": "Animated progression from unit circle to sine wave",
      "url": "",
      "json_data": [
        {
          "diagramType": "angle",
          "fromAngleDeg": 0,
          "toAngleDeg": 90,
          "duration": 2000,
          "transitionType": "fade"
        },
        {
          "diagramType": "graph",
          "equation": "sin(x)",
          "graphType": "function",
          "domain": {"min": 0, "max": 1.57},
          "duration": 3000,
          "transitionType": "slide"
        },
        {
          "diagramType": "graph",
          "equation": "sin(x)",
          "graphType": "function",
          "domain": {"min": 0, "max": 6.28},
          "duration": 2000,
          "transitionType": "zoom"
        }
      ],
      "order": 4,
      "duration": 210
    },
    {
      "id": "hash",
      "type": "audio",
      "text": "Audio explanation of sine function properties",
      "url": "https://example.com/sine-properties.mp3",
      "json_data": null,
      "order": 5,
      "duration": 90
    }
  ]
}
```

### Example 2: Quiz with Multimedia Questions

```json
{
  "quiz": {
    "title": "Trigonometry Assessment",
    "duration": 600,
    "difficulty_level": "medium",
    "instruction": "Answer all questions. You may use the interactive diagrams to help solve problems.",
    "questions": [
        {
          "sections": [
            {
              "id": "hash",
              "type": "text",
              "text": "**Question 1**: What is the value of sin(π/2)?",
              "url": "",
              "json_data": null,
              "order": 1
            },
            {
              "id": "hash",
              "type": "diagram",
              "text": "Unit circle reference",
              "url": "",
              "json_data": {
                "diagramType": "angle",
                "fromAngleDeg": 0,
                "toAngleDeg": 90,
                "draggable": "none",
                "angleLabel": "π/2",
                "arcColor": "#2196f3"
              },
              "order": 2
            }
          ],
          "options": [
            {
              "id": "q1_option_a",
              "type": "text",
              "text": "A) 0",
              "url": "",
              "json_data": null,
              "order": 1
            },
            {
              "id": "q1_option_b",
              "type": "text",
              "text": "B) 1",
              "url": "",
              "json_data": null,
              "order": 2
            },
            {
              "id": "q1_option_c",
              "type": "text",
              "text": "C) -1",
              "url": "",
              "json_data": null,
              "order": 3
            },
            {
              "id": "q1_option_d",
              "type": "text",
              "text": "D) π/2",
              "url": "",
              "json_data": null,
              "order": 4
            }
          ],
          "answer": "q1_option_b",
          "answer_explanation": "sin(π/2) = 1 because at 90 degrees, the y-coordinate on the unit circle is 1.",
          "order": 1,
          "marks": 10
        }
      ]
  }
}
```

## Best Practices

### Content Organization
1. **Logical Progression**: Order sections to build understanding incrementally
2. **Mixed Media**: Combine text, visuals, and interactive content for engagement
3. **Consistent Timing**: Set realistic duration estimates for pacing
4. **Clear Descriptions**: Use descriptive text for all content types

### Hashing Considerations
1. **Content Consistency**: Identical content should have identical hash inputs
2. **Whitespace Normalization**: Trim unnecessary whitespace in text content
3. **JSON Formatting**: Use consistent JSON formatting for diagram data
4. **URL Standardization**: Use consistent URL formats (with/without trailing slashes)

### Diagram Integration
1. **Equation Preference**: Use equation-based graphs when possible for mathematical content
2. **Interactive Elements**: Enable interactivity for exploratory learning
3. **Animation Timing**: Keep individual diagram durations between 2-6 seconds
4. **Accessibility**: Provide text descriptions for all visual content

### Performance Optimization
1. **Media Compression**: Optimize video/audio file sizes
2. **Diagram Complexity**: Limit data points in graphs (< 1000 for smooth performance)
3. **Sequence Length**: Keep animation sequences under 10 diagrams
4. **Caching Strategy**: Leverage content hashing for efficient caching

### Quality Assurance
1. **Content Validation**: Verify all URLs are accessible
2. **Diagram Testing**: Test interactive elements across devices
3. **Duration Accuracy**: Validate content duration estimates
4. **Hash Uniqueness**: Ensure content changes result in new hashes

### Example Hash Generation

```javascript
// Example of content that would generate the same hash
const content1 = {
  type: "diagram",
  url: "",
  text: "Sine wave visualization",
  json_data: {
    diagramType: "graph",
    equation: "sin(x)",
    graphType: "function"
  }
};

const content2 = {
  type: "diagram",
  url: "",
  text: "Sine wave visualization",
  json_data: {
    diagramType: "graph",
    equation: "sin(x)",
    graphType: "function"
  }
};

// These would generate identical hashes and be deduplicated
// Even if they appear in different lessons/courses
```

This comprehensive guide ensures consistent, efficient, and maintainable educational content creation with proper deduplication and rich interactive elements.
