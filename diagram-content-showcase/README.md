# Diagram Builder

An AI-powered interactive diagram generator that creates mathematical and educational diagrams from natural language descriptions. Built with React, TypeScript, and powered by OpenAI's GPT models.

## 🌟 Features

- **AI-Powered Generation**: Convert natural language descriptions into interactive diagrams
- **Multiple Diagram Types**: Support for Venn diagrams, geometric polygons, angle diagrams, and number line graphs
- **Interactive Elements**: Draggable components, real-time updates, and dynamic animations
- **Animated Sequences**: Create smooth transitions between different diagram states
- **Audio Integration**: Synchronized audio narration for educational content
- **Modern UI**: Clean, responsive interface built with Tailwind CSS

## 📊 Supported Diagram Types

### 1. Venn Diagrams
- Set relationships and intersections
- Customizable labels and sizes
- Support for multiple sets

### 2. Polygon Diagrams
- Geometric shapes with custom vertices
- Labeled edges and vertices
- Customizable colors and styling

### 3. Angle Diagrams
- Interactive angle measurement
- Draggable angle handles
- Degree and radian support
- Custom labels and styling

### 4. Number Line Graphs
- Mathematical equation visualization
- Interactive zooming and panning
- Support for complex mathematical expressions

## 🚀 Getting Started

### Prerequisites

- Node.js (v18 or higher)
- pnpm (recommended) or npm
- OpenAI API key

### Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/diagram-builder.git
cd diagram-builder
```

2. Install dependencies:
```bash
pnpm install
```

3. Set up environment variables:
Create a `.env` file in the root directory with your OpenAI API key:
```env
VITE_OPENAI_API_KEY=your_openai_api_key_here
```

4. Start the development server:
```bash
pnpm dev
```

5. Open your browser and navigate to `http://localhost:5173`

## 🛠️ Available Scripts

- `pnpm dev` - Start development server
- `pnpm build` - Build for production
- `pnpm preview` - Preview production build
- `pnpm lint` - Run ESLint

## 🏗️ Project Structure

```
src/
├── components/
│   ├── diagrams/           # Individual diagram components
│   │   ├── VennDiagram.tsx
│   │   ├── PolygonDiagram.tsx
│   │   ├── AngleDiagram.tsx
│   │   └── NumberLineGraph/
│   ├── CompositeDiagram.tsx # Main diagram renderer
│   ├── AnimatedDiagramSequence.tsx
│   ├── animations.ts       # Animation utilities
│   └── transitions.ts      # Transition effects
├── chains/                 # AI processing chains
│   ├── venn/              # Venn diagram AI logic
│   ├── polygon/           # Polygon diagram AI logic
│   └── angle/             # Angle diagram AI logic
├── diagramTypes.ts        # TypeScript type definitions
├── diagramGenerator.ts    # Main AI orchestration
└── App.tsx               # Main application component
```

## 💡 Usage Examples

### Basic Diagram Generation

1. **Venn Diagrams**:
   - "Create a Venn diagram showing the intersection of sets A and B"
   - "Show three overlapping circles representing students who like math, science, and art"

2. **Polygon Diagrams**:
   - "Draw a triangle with sides labeled 3cm, 4cm, and 5cm"
   - "Create an octagon with all sides equal"

3. **Angle Diagrams**:
   - "Show a 45-degree angle with vertex at point B"
   - "Create an angle ABC measuring 90 degrees"

4. **Number Line Graphs**:
   - "Plot the equation y = x² - 3x + 2"
   - "Show the graph of sin(x) from -2π to 2π"

## 🔧 Technical Details

### Key Dependencies

- **React 18** - Modern React with hooks and concurrent features
- **TypeScript** - Type-safe development
- **D3.js** - Data visualization and SVG manipulation
- **Framer Motion** - Smooth animations and transitions
- **LangChain** - AI workflow orchestration
- **OpenAI GPT-4** - Natural language processing
- **Tailwind CSS** - Utility-first styling
- **Vite** - Fast build tool and development server
- **MathJS** - Mathematical expression parsing and evaluation

## 🎨 Customization

### Adding New Diagram Types

1. Define the diagram parameters in `diagramTypes.ts`
2. Create a React component in `src/components/diagrams/`
3. Add AI processing chain in `src/chains/`
4. Update the route selector in `diagramGenerator.ts`
5. Add a new route in `App.tsx`

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🐛 Troubleshooting

### Common Issues

1. **OpenAI API Key**: Ensure your API key is correctly set in the `.env` file
2. **Build Errors**: Try deleting `node_modules` and running `pnpm install` again
3. **Animation Performance**: Reduce the number of simultaneous animations for better performance

---

Built with ❤️ using React, TypeScript, and AI
