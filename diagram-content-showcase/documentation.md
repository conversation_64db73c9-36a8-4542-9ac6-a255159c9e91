# Comprehensive Diagram System Documentation

## Table of Contents
1. [Overview](#overview)
2. [Diagram Types](#diagram-types)
3. [Animation System](#animation-system)
4. [Sequence System](#sequence-system)
5. [AI Integration](#ai-integration)
6. [Component Architecture](#component-architecture)
7. [Configuration Reference](#configuration-reference)
8. [Examples](#examples)

## Overview

The Diagram System is a comprehensive, AI-powered visualization platform that supports multiple diagram types with advanced animations, transitions, and interactive features. The system is built with TypeScript, React, and leverages multiple visualization libraries for optimal performance and flexibility.

### Core Features
- **AI-Powered Generation**: Natural language to diagram conversion
- **Multiple Diagram Types**: Venn, Polygon, Angle, and Graph diagrams
- **Advanced Animations**: Smooth transitions with customizable easing
- **Interactive Components**: Zoom, pan, selection, and real-time controls
- **Sequence Support**: Animated diagram sequences with timing control
- **Responsive Design**: Adapts to different screen sizes and containers

## Diagram Types

### 1. Venn Diagrams (`VennDiagramParams`)

Venn diagrams represent set relationships, overlaps, and intersections.

```typescript
interface VennDiagramParams extends BaseDiagramParams {
  diagramType: "venn";
  data: VennSetItem[];
}

interface VennSetItem {
  sets: string[];      // e.g., ["A"], ["A", "B"] for intersection
  size: number;        // cardinality (size of this region)
  label?: string;      // user-defined label for the region
}
```

**Example:**
```typescript
const vennDiagram: VennDiagramParams = {
  diagramType: "venn",
  data: [
    { sets: ["A"], size: 10, label: "Only A" },
    { sets: ["B"], size: 8, label: "Only B" },
    { sets: ["A", "B"], size: 5, label: "A ∩ B" },
  ]
};
```

**AI Prompts that generate Venn diagrams:**
- "Create a Venn diagram showing the overlap between cats and dogs"
- "Show set relationships between A, B, and C"
- "Generate a Venn diagram for students who like math and science"

### 2. Polygon Diagrams (`PolygonDiagramParams`)

Polygon diagrams represent geometric shapes with customizable vertices and styling.

```typescript
interface PolygonDiagramParams extends BaseDiagramParams {
  diagramType: "polygon";
  width: number;
  height: number;
  points: { x: number; y: number }[];
  fill?: string;
  stroke?: string;
  strokeWidth?: number;
  pointLabels?: string[];    // Labels for each vertex
  lineLabels?: string[];     // Labels for each line segment
}
```

**Example:**
```typescript
const triangle: PolygonDiagramParams = {
  diagramType: "polygon",
  width: 400,
  height: 400,
  points: [
    { x: 200, y: 50 },   // Top vertex
    { x: 50, y: 350 },   // Bottom left
    { x: 350, y: 350 }   // Bottom right
  ],
  fill: "#e3f2fd",
  stroke: "#1976d2",
  strokeWidth: 3,
  pointLabels: ["A", "B", "C"],
  lineLabels: ["AB", "BC", "CA"]
};
```

**AI Prompts that generate Polygon diagrams:**
- "Draw a triangle with vertices at specific coordinates"
- "Create a pentagon with labeled vertices"
- "Show a rectangle with dimensions 100x200"

### 3. Angle Diagrams (`AngleDiagramParams`)

Angle diagrams represent angular measurements with interactive controls.

```typescript
interface AngleDiagramParams extends BaseDiagramParams {
  diagramType: "angle";
  fromAngleDeg?: number;        // Starting angle (default: 0)
  toAngleDeg: number;           // Ending angle
  draggable?: "from" | "to" | "both" | "none";
  onAngleChange?: (update: { fromAngleDeg?: number; toAngleDeg?: number }) => void;
  snapIncrement?: number;       // Snap to increments (e.g., 15 degrees)
  drawClockwise?: boolean;      // Direction of angle measurement
  arcColor?: string;
  lineColor?: string;
  labelColor?: string;
  fontSize?: number;
  labels?: { _from?: string; _vertex?: string; _to?: string };
  angleLabel?: string;
  highlightColor?: string;
  showHandles?: boolean;        // Show interactive handles
}
```

**Example:**
```typescript
const angle: AngleDiagramParams = {
  diagramType: "angle",
  fromAngleDeg: 0,
  toAngleDeg: 45,
  draggable: "both",
  snapIncrement: 5,
  arcColor: "#ff6b6b",
  lineColor: "#4ecdc4",
  labels: { _from: "A", _vertex: "O", _to: "B" },
  angleLabel: "45°",
  showHandles: true
};
```

**AI Prompts that generate Angle diagrams:**
- "Show an angle of 45 degrees"
- "Create an interactive angle measurement tool"
- "Draw angle ABC with 60 degrees"

### 4. Graph Diagrams (`GraphDiagramParams`)

Graph diagrams represent data visualizations with multiple chart types and interactive features.

```typescript
interface GraphDiagramParams extends BaseDiagramParams {
  diagramType: "graph";
  mode: 'plot' | 'interactive';     // Static or interactive mode
  width?: number;
  height?: number;
  series: GraphSeries[];            // Data series to plot
  xAxis?: GraphAxis;
  yAxis?: GraphAxis;
  legend?: GraphLegend;
  tooltip?: GraphTooltip;
  interaction?: GraphInteraction;
  animation?: GraphAnimation;
  backgroundColor?: string;
  margin?: { top?: number; right?: number; bottom?: number; left?: number };
  theme?: 'light' | 'dark' | 'custom';
  customTheme?: CustomTheme;
}

interface GraphSeries {
  id: string;
  name: string;
  data: DataPoint[];
  type: 'line' | 'scatter' | 'bar' | 'area' | 'spline' | 'step';
  color?: string;
  strokeWidth?: number;
  fillOpacity?: number;
  visible?: boolean;
  animated?: boolean;
  animationDuration?: number;
  animationDelay?: number;
}

interface DataPoint {
  x: number;
  y: number;
  label?: string;
  color?: string;
  size?: number;
}
```

**Example:**
```typescript
const lineChart: GraphDiagramParams = {
  diagramType: "graph",
  mode: "interactive",
  series: [
    {
      id: "sales",
      name: "Monthly Sales",
      data: [
        { x: 1, y: 120 },
        { x: 2, y: 150 },
        { x: 3, y: 180 },
        { x: 4, y: 140 }
      ],
      type: "line",
      color: "#0077ff",
      animated: true,
      animationDuration: 2000
    }
  ],
  xAxis: { label: "Month", gridVisible: true },
  yAxis: { label: "Sales ($k)", gridVisible: true },
  interaction: { zoom: true, pan: true, hover: true }
};
```

**AI Prompts that generate Graph diagrams:**
- "Create a line chart showing sales data over 12 months"
- "Plot a sine wave from 0 to 2π"
- "Show a scatter plot with random data points"
- "Generate a bar chart comparing quarterly revenue"

## Animation System

The animation system provides smooth transitions and visual effects for all diagram types.

### Transition Types (`TransitionType`)

```typescript
type TransitionType =
  | "fade"          // Opacity transition
  | "zoom"          // Scale transition
  | "slide"         // Position transition
  | "flip"          // 3D flip effect
  | "drop"          // Drop from above
  | "rotate"        // Rotation transition
  | "warp"          // Distortion effect
  | "diagonal"      // Diagonal slide
  | "distortFade"   // Combined distortion and fade
  | "ripple"        // Ripple effect
  | "glitch"        // Glitch effect
  | "explode"       // Explosion effect
  | "orbit";        // Orbital motion
```

### Animation Types (`AnimationType`)

```typescript
type AnimationType =
  | "infiniteRotate"  // Continuous rotation
  | "infiniteBounce"  // Continuous bouncing
  | "infinitePulse"   // Continuous pulsing
  | "infiniteWiggle"  // Continuous wiggling
  | "infiniteGlow";   // Continuous glowing
```

### Graph Animation Configuration

```typescript
interface GraphAnimation {
  enabled?: boolean;
  duration?: number;    // Animation duration in milliseconds
  easing?: 'linear' | 'easeIn' | 'easeOut' | 'easeInOut' | 'circIn' | 'circOut' | 'circInOut' | 'backIn' | 'backOut' | 'backInOut';
  stagger?: number;     // Delay between series animations
  onComplete?: () => void;
}
```

**Example:**
```typescript
const animatedGraph: GraphDiagramParams = {
  // ... other properties
  animation: {
    enabled: true,
    duration: 2000,
    easing: "easeOut",
    stagger: 500
  }
};

## Sequence System

The sequence system allows creating animated presentations with multiple diagrams.

### Basic Sequence (`AnimatedDiagramSequence`)

```typescript
interface TimedDiagramDefinition extends DiagramDefinition {
  duration: number;        // How long to display this diagram (ms)
  transitionType?: TransitionType;
  animationType?: AnimationType;
}

// Example sequence
const sequence: TimedDiagramDefinition[] = [
  {
    diagramType: "venn",
    data: [{ sets: ["A"], size: 10 }],
    duration: 3000,
    transitionType: "fade"
  },
  {
    diagramType: "polygon",
    width: 400,
    height: 400,
    points: [{ x: 200, y: 100 }, { x: 100, y: 300 }, { x: 300, y: 300 }],
    duration: 3000,
    transitionType: "zoom"
  }
];
```

### Advanced Sequence with Audio (`CompleteAnimatedDiagramSequence`)

```typescript
interface TimedDiagramDefinition extends DiagramDefinition {
  duration?: number;
  audioUrl?: string;       // Audio file to play during this diagram
  transitionType?: TransitionType;
  animationType?: AnimationType;
}

// Example with audio
const audioSequence: TimedDiagramDefinition[] = [
  {
    diagramType: "graph",
    mode: "plot",
    series: [{ id: "1", name: "Data", data: [...], type: "line" }],
    duration: 5000,
    audioUrl: "/narration1.mp3",
    transitionType: "slide"
  }
];
```

### Sequence Controls

```typescript
interface AnimatedDiagramSequenceProps {
  sequence: TimedDiagramDefinition[];
  loop?: boolean;           // Whether to loop the sequence
  playOnRender?: boolean;   // Auto-play when component mounts
}
```

## AI Integration

The system includes AI-powered diagram generation using OpenAI's GPT models.

### Route Selection

The AI automatically determines the appropriate diagram type based on natural language input:

```typescript
// Route determination logic
const routePrompt = `
Analyze the prompt and determine the best diagram type:
- If it describes set relationships (overlapping, disjoint, intersections), return "venn".
- If it describes geometric shapes (vertices, edges), return "polygon".
- If it describes angles (degrees, radians, turning, arc, ∠ABC), return "angle".
- If it describes data visualization, charts, graphs, plots, functions, mathematical equations, line charts, bar charts, scatter plots, or any data representation, return "graph".

Return only one of: "venn", "polygon", "angle", "graph".
`;
```

### Usage Examples

```typescript
// Generate diagram from natural language
const diagramDefinition = await getDiagramDefinitionFromPrompt(
  "Create a line chart showing temperature over time"
);

// The AI will automatically:
// 1. Determine this should be a "graph" type
// 2. Generate appropriate data points
// 3. Configure axes, styling, and interactions
// 4. Return a complete GraphDiagramParams object
```

### Supported Prompts by Type

**Venn Diagrams:**
- "Show the relationship between cats and dogs"
- "Create a Venn diagram for students who like math and science"
- "Display set A, B, and C with their intersections"

**Polygon Diagrams:**
- "Draw a triangle with vertices at (0,0), (100,0), (50,100)"
- "Create a pentagon with equal sides"
- "Show a rectangle 200 pixels wide and 150 pixels tall"

**Angle Diagrams:**
- "Show an angle of 45 degrees"
- "Create angle ABC measuring 60 degrees"
- "Display a right angle with interactive controls"

**Graph Diagrams:**
- "Plot y = x² from -5 to 5"
- "Create a bar chart of monthly sales data"
- "Show a scatter plot of height vs weight"
- "Generate a line chart with multiple series"

## Component Architecture

### Core Components

#### 1. `CompositeDiagram`
The main component that renders any diagram type based on the definition.

```typescript
interface CompositeDiagramProps {
  definition: DiagramDefinition;
}

// Usage
<CompositeDiagram definition={diagramDefinition} />
```

#### 2. `CompositeDiagramWithTransitions`
Enhanced version with transition and animation support.

```typescript
interface CompositeDiagramWithTransitionsProps {
  definition: DiagramDefinition;
  transitionType?: TransitionType;
  animationType?: AnimationType;
}

// Usage
<CompositeDiagramWithTransitions
  definition={diagramDefinition}
  transitionType="fade"
  animationType="infinitePulse"
/>
```

#### 3. `AnimatedDiagramSequence`
Component for playing sequences of diagrams.

```typescript
interface AnimatedDiagramSequenceProps {
  sequence: TimedDiagramDefinition[];
  loop?: boolean;
}

// Usage
<AnimatedDiagramSequence sequence={sequenceData} loop={true} />
```

#### 4. `GraphComponent`
Advanced graph visualization component.

```typescript
interface GraphComponentProps {
  params: GraphDiagramParams;
  onDataChange?: (series: GraphSeries[]) => void;
  onInteractionChange?: (interaction: any) => void;
}

// Usage
<GraphComponent
  params={graphParams}
  onDataChange={handleDataChange}
  onInteractionChange={handleInteractionChange}
/>
```

### Specialized Graph Components

#### 1. `PlotlyGraph`
High-performance graph component using Plotly.js for complex visualizations.

#### 2. `VisxGraph`
Lightweight graph component using Visx for simple, customizable charts.

#### 3. `InteractiveControls`
Control panel for interactive graph features.

## Configuration Reference

### Graph Axis Configuration

```typescript
interface GraphAxis {
  label?: string;           // Axis label
  min?: number;            // Minimum value
  max?: number;            // Maximum value
  tickCount?: number;      // Number of ticks
  tickFormat?: string;     // Tick format string
  gridVisible?: boolean;   // Show/hide grid lines
  gridColor?: string;      // Grid line color
  gridOpacity?: number;    // Grid line opacity (0-1)
  labelColor?: string;     // Label text color
  labelFontSize?: number;  // Label font size
}
```

### Graph Legend Configuration

```typescript
interface GraphLegend {
  visible?: boolean;
  position?: 'top' | 'bottom' | 'left' | 'right' | 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  orientation?: 'horizontal' | 'vertical';
  backgroundColor?: string;
  borderColor?: string;
  textColor?: string;
  fontSize?: number;
}
```

### Graph Tooltip Configuration

```typescript
interface GraphTooltip {
  enabled?: boolean;
  format?: string;         // Format string like "(%{x}, %{y})"
  backgroundColor?: string;
  textColor?: string;
  borderColor?: string;
  fontSize?: number;
}
```

### Graph Interaction Configuration

```typescript
interface GraphInteraction {
  zoom?: boolean;          // Enable zoom functionality
  pan?: boolean;           // Enable pan functionality
  select?: boolean;        // Enable point selection
  brush?: boolean;         // Enable brush selection
  crosshair?: boolean;     // Enable crosshair cursor
  hover?: boolean;         // Enable hover effects
}

## Examples

### Complete Venn Diagram Example

```typescript
import { VennDiagramParams } from './diagramTypes';
import CompositeDiagram from './components/CompositeDiagram';

const vennExample: VennDiagramParams = {
  diagramType: "venn",
  data: [
    { sets: ["Math"], size: 15, label: "Math Only" },
    { sets: ["Science"], size: 12, label: "Science Only" },
    { sets: ["Math", "Science"], size: 8, label: "Both" }
  ]
};

function VennExample() {
  return <CompositeDiagram definition={vennExample} />;
}
```

### Complete Interactive Graph Example

```typescript
import { GraphDiagramParams } from './diagramTypes';
import GraphComponent from './components/diagrams/GraphComponent';

const interactiveGraph: GraphDiagramParams = {
  diagramType: "graph",
  mode: "interactive",
  width: 800,
  height: 500,
  series: [
    {
      id: "temperature",
      name: "Temperature (°C)",
      data: [
        { x: 0, y: 20, label: "Jan" },
        { x: 1, y: 22, label: "Feb" },
        { x: 2, y: 25, label: "Mar" },
        { x: 3, y: 28, label: "Apr" },
        { x: 4, y: 32, label: "May" },
        { x: 5, y: 35, label: "Jun" }
      ],
      type: "line",
      color: "#ff6b6b",
      strokeWidth: 3,
      animated: true,
      animationDuration: 2000
    },
    {
      id: "humidity",
      name: "Humidity (%)",
      data: [
        { x: 0, y: 65 },
        { x: 1, y: 62 },
        { x: 2, y: 58 },
        { x: 3, y: 55 },
        { x: 4, y: 52 },
        { x: 5, y: 48 }
      ],
      type: "area",
      color: "#4ecdc4",
      fillOpacity: 0.3,
      animated: true,
      animationDuration: 2000,
      animationDelay: 500
    }
  ],
  xAxis: {
    label: "Month",
    gridVisible: true,
    gridColor: "#e0e0e0",
    tickCount: 6
  },
  yAxis: {
    label: "Value",
    gridVisible: true,
    gridColor: "#e0e0e0",
    min: 0,
    max: 100
  },
  legend: {
    visible: true,
    position: "top-right",
    backgroundColor: "rgba(255, 255, 255, 0.9)",
    borderColor: "#ccc"
  },
  tooltip: {
    enabled: true,
    format: "%{x}: %{y}",
    backgroundColor: "rgba(0, 0, 0, 0.8)",
    textColor: "white"
  },
  interaction: {
    zoom: true,
    pan: true,
    hover: true,
    crosshair: true,
    select: true
  },
  animation: {
    enabled: true,
    duration: 2000,
    easing: "easeOut",
    stagger: 500
  },
  backgroundColor: "#fafafa",
  theme: "light"
};

function InteractiveGraphExample() {
  const handleDataChange = (series: GraphSeries[]) => {
    console.log('Data changed:', series);
  };

  const handleInteractionChange = (interaction: any) => {
    console.log('Interaction:', interaction);
  };

  return (
    <GraphComponent
      params={interactiveGraph}
      onDataChange={handleDataChange}
      onInteractionChange={handleInteractionChange}
    />
  );
}
```

### Complete Animated Sequence Example

```typescript
import { TimedDiagramDefinition } from './diagramTypes';
import AnimatedDiagramSequence from './components/AnimatedDiagramSequence';

const educationalSequence: TimedDiagramDefinition[] = [
  // Introduction slide
  {
    diagramType: "venn",
    data: [
      { sets: ["A"], size: 10, label: "Set A" },
      { sets: ["B"], size: 8, label: "Set B" }
    ],
    duration: 3000,
    transitionType: "fade"
  },
  // Show intersection
  {
    diagramType: "venn",
    data: [
      { sets: ["A"], size: 10, label: "Set A" },
      { sets: ["B"], size: 8, label: "Set B" },
      { sets: ["A", "B"], size: 5, label: "A ∩ B" }
    ],
    duration: 4000,
    transitionType: "zoom"
  },
  // Show data visualization
  {
    diagramType: "graph",
    mode: "plot",
    series: [
      {
        id: "intersection",
        name: "Intersection Size",
        data: [
          { x: 1, y: 3 },
          { x: 2, y: 5 },
          { x: 3, y: 4 },
          { x: 4, y: 6 }
        ],
        type: "bar",
        color: "#4ecdc4"
      }
    ],
    xAxis: { label: "Time Period" },
    yAxis: { label: "Size" },
    duration: 3000,
    transitionType: "slide"
  }
];

function EducationalSequence() {
  return (
    <AnimatedDiagramSequence
      sequence={educationalSequence}
      loop={false}
    />
  );
}
```

### AI-Generated Diagram Example

```typescript
import { getDiagramDefinitionFromPrompt } from './diagramGenerator';
import CompositeDiagram from './components/CompositeDiagram';

function AIGeneratedDiagram() {
  const [diagram, setDiagram] = useState(null);
  const [loading, setLoading] = useState(false);

  const generateDiagram = async (prompt: string) => {
    setLoading(true);
    try {
      const definition = await getDiagramDefinitionFromPrompt(prompt);
      setDiagram(definition);
    } catch (error) {
      console.error('Error generating diagram:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <button onClick={() => generateDiagram("Create a sine wave graph")}>
        Generate Sine Wave
      </button>
      <button onClick={() => generateDiagram("Show Venn diagram of cats and dogs")}>
        Generate Venn Diagram
      </button>
      <button onClick={() => generateDiagram("Draw a triangle with 60 degree angles")}>
        Generate Triangle
      </button>

      {loading && <p>Generating diagram...</p>}
      {diagram && <CompositeDiagram definition={diagram} />}
    </div>
  );
}
```

## Best Practices

### Performance Optimization
1. **Use appropriate modes**: Use `plot` mode for static displays, `interactive` for dynamic content
2. **Limit data points**: Keep datasets under 1000 points for smooth interactions
3. **Optimize animations**: Use shorter durations for better performance
4. **Lazy loading**: Components automatically choose optimal rendering libraries

### Accessibility
1. **Provide labels**: Always include axis labels and series names
2. **Color contrast**: Ensure sufficient contrast for all visual elements
3. **Alternative text**: Include descriptive text for screen readers
4. **Keyboard navigation**: Interactive elements support keyboard controls

### User Experience
1. **Progressive disclosure**: Start with simple views, add complexity gradually
2. **Consistent styling**: Use theme system for consistent appearance
3. **Responsive design**: Components adapt to container sizes
4. **Error handling**: Provide meaningful error messages and fallbacks

### Development Guidelines
1. **Type safety**: Use TypeScript interfaces for all configurations
2. **Validation**: Validate data before rendering
3. **Error boundaries**: Implement error boundaries for robust applications
4. **Testing**: Test with various data sizes and edge cases

This comprehensive documentation covers all aspects of the diagram system, from basic usage to advanced configurations and best practices.
```
```
```
